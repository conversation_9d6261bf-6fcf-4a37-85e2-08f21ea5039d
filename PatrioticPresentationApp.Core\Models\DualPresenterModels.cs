using System;
using System.Collections.Generic;

namespace PatrioticPresentationApp.Core.Models
{
    /// <summary>
    /// Represents a connected presenter in dual-presenter mode
    /// </summary>
    public class ConnectedPresenter
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; }
        public PresenterRole Role { get; set; }
        public DateTime ConnectionTime { get; set; } = DateTime.Now;
        public string IPAddress { get; set; }
        public bool HasControl { get; set; }
        public PresenterStatus Status { get; set; } = PresenterStatus.Connected;
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Annotation data for collaborative features
    /// </summary>
    public class AnnotationData
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public int SlideIndex { get; set; }
        public AnnotationType Type { get; set; }
        public string Content { get; set; }
        public AnnotationStyle Style { get; set; } = new AnnotationStyle();
        public AnnotationPosition Position { get; set; } = new AnnotationPosition();
        public string CreatedBy { get; set; }
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        public bool IsVisible { get; set; } = true;
        public TimeSpan Duration { get; set; } = TimeSpan.FromSeconds(5);
    }

    /// <summary>
    /// Annotation style properties
    /// </summary>
    public class AnnotationStyle
    {
        public string Color { get; set; } = "#FF0000";
        public double Thickness { get; set; } = 2.0;
        public string FontFamily { get; set; } = "Arial";
        public double FontSize { get; set; } = 14.0;
        public bool IsBold { get; set; } = false;
        public bool IsItalic { get; set; } = false;
        public double Opacity { get; set; } = 1.0;
    }

    /// <summary>
    /// Annotation position and size
    /// </summary>
    public class AnnotationPosition
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
        public double Rotation { get; set; } = 0.0;
    }

    /// <summary>
    /// Pointer data for collaborative cursor
    /// </summary>
    public class PointerData
    {
        public string PresenterId { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
        public PointerType Type { get; set; } = PointerType.Cursor;
        public string Color { get; set; } = "#FF0000";
        public bool IsVisible { get; set; } = true;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Screen region for sharing
    /// </summary>
    public class ScreenRegion
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
        public string Title { get; set; }
        public byte[] ImageData { get; set; }
        public DateTime CaptureTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Session statistics for dual presenter mode
    /// </summary>
    public class SessionStatistics
    {
        public string SessionId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public int TotalPresenters { get; set; }
        public int ControlTransfers { get; set; }
        public int SynchronizationEvents { get; set; }
        public int CollaborationEvents { get; set; }
        public Dictionary<string, int> PresenterActivityCounts { get; set; } = new Dictionary<string, int>();
        public List<SessionEvent> Events { get; set; } = new List<SessionEvent>();
    }

    /// <summary>
    /// Session event for tracking
    /// </summary>
    public class SessionEvent
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public SessionEventType Type { get; set; }
        public string PresenterId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
    }

    // Enumerations
    public enum PresenterRole
    {
        Primary,
        Secondary,
        Observer,
        Moderator
    }

    public enum PresenterStatus
    {
        Connected,
        Disconnected,
        Reconnecting,
        Inactive,
        Presenting
    }

    public enum AnnotationType
    {
        Text,
        Arrow,
        Rectangle,
        Circle,
        Line,
        Highlight,
        Freehand
    }

    public enum PointerType
    {
        Cursor,
        Laser,
        Highlight,
        Arrow
    }

    public enum ControlType
    {
        PresentationControl,
        SlideNavigation,
        AudioControl,
        AnnotationControl,
        ScreenShare
    }

    public enum SynchronizationType
    {
        SlideChange,
        RevealItem,
        AudioControl,
        PresentationState,
        Annotation,
        PointerLocation
    }

    public enum CollaborationType
    {
        Annotation,
        ChatMessage,
        PointerLocation,
        ScreenShare,
        FileTransfer
    }

    public enum AudioAction
    {
        Play,
        Pause,
        Stop,
        VolumeChange,
        Seek
    }

    public enum PresentationState
    {
        Stopped,
        Playing,
        Paused,
        Loading,
        Error
    }

    public enum SessionEventType
    {
        PresenterJoined,
        PresenterLeft,
        ControlTransferred,
        SlideChanged,
        AnnotationAdded,
        ChatMessage,
        ScreenShared,
        AudioPlayed,
        Error
    }

    // Event Arguments Classes
    public class PresenterEventArgs : EventArgs
    {
        public string PresenterId { get; set; }
        public string PresenterName { get; set; }
        public PresenterRole Role { get; set; }
        public DateTime ConnectionTime { get; set; }
        public string Message { get; set; }
    }

    public class PresenterControlEventArgs : EventArgs
    {
        public string FromPresenterId { get; set; }
        public string ToPresenterId { get; set; }
        public ControlType ControlType { get; set; }
        public DateTime Timestamp { get; set; }
        public string Reason { get; set; }
    }

    public class SynchronizationEventArgs : EventArgs
    {
        public SynchronizationType Type { get; set; }
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        public string SenderId { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class CollaborationEventArgs : EventArgs
    {
        public CollaborationType Type { get; set; }
        public object Data { get; set; }
        public string SenderId { get; set; }
        public string ReceiverId { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
