using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace PatrioticPresentationApp.Core.Models
{
    /// <summary>
    /// Represents the complete patriotic presentation
    /// </summary>
    public class PresentationModel : INotifyPropertyChanged
    {
        private int _currentSlideIndex;
        private bool _isPlaying;
        private PresentationMode _mode;
        private string _title;

        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged(nameof(Title));
            }
        }

        public string Description { get; set; }
        public string Author { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string Version { get; set; } = "1.0";

        public int CurrentSlideIndex
        {
            get => _currentSlideIndex;
            set
            {
                _currentSlideIndex = value;
                OnPropertyChanged(nameof(CurrentSlideIndex));
                OnPropertyChanged(nameof(CurrentSlide));
                OnPropertyChanged(nameof(Progress));
            }
        }

        public bool IsPlaying
        {
            get => _isPlaying;
            set
            {
                _isPlaying = value;
                OnPropertyChanged(nameof(IsPlaying));
            }
        }

        public PresentationMode Mode
        {
            get => _mode;
            set
            {
                _mode = value;
                OnPropertyChanged(nameof(Mode));
            }
        }

        public List<SlideModel> Slides { get; set; } = new List<SlideModel>();
        public PresentationSettings Settings { get; set; } = new PresentationSettings();
        public PresentationStatistics Statistics { get; set; } = new PresentationStatistics();

        // Computed Properties
        public SlideModel CurrentSlide => 
            CurrentSlideIndex >= 0 && CurrentSlideIndex < Slides.Count ? 
            Slides[CurrentSlideIndex] : null;

        public int TotalSlides => Slides.Count;

        public double Progress => 
            TotalSlides > 0 ? (double)CurrentSlideIndex / (TotalSlides - 1) * 100 : 0;

        public TimeSpan EstimatedDuration
        {
            get
            {
                var total = TimeSpan.Zero;
                foreach (var slide in Slides)
                {
                    total = total.Add(slide.Duration);
                }
                return total;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Navigation Methods
        public bool CanGoNext() => CurrentSlideIndex < TotalSlides - 1;
        public bool CanGoPrevious() => CurrentSlideIndex > 0;

        public void NextSlide()
        {
            if (CanGoNext())
            {
                CurrentSlideIndex++;
                Statistics.RecordSlideTransition(CurrentSlideIndex, DateTime.Now);
            }
        }

        public void PreviousSlide()
        {
            if (CanGoPrevious())
            {
                CurrentSlideIndex--;
                Statistics.RecordSlideTransition(CurrentSlideIndex, DateTime.Now);
            }
        }

        public void GoToSlide(int index)
        {
            if (index >= 0 && index < TotalSlides)
            {
                CurrentSlideIndex = index;
                Statistics.RecordSlideTransition(CurrentSlideIndex, DateTime.Now);
            }
        }
    }

    /// <summary>
    /// Presentation configuration settings
    /// </summary>
    public class PresentationSettings
    {
        public TimeSpan DefaultSlideDuration { get; set; } = TimeSpan.FromSeconds(5);
        public TimeSpan TransitionDuration { get; set; } = TimeSpan.FromMilliseconds(800);
        public bool AutoPlay { get; set; } = false;
        public bool EnableAudio { get; set; } = true;
        public bool EnableAnimations { get; set; } = true;
        public bool EnableKeyboardNavigation { get; set; } = true;
        public bool EnableMouseNavigation { get; set; } = true;
        public bool EnableTouchNavigation { get; set; } = true;
        public double AudioVolume { get; set; } = 0.8;
        public bool LoopPresentation { get; set; } = false;
        public bool ShowProgressBar { get; set; } = true;
        public bool ShowSlideNumbers { get; set; } = true;
        public bool EnableFullScreen { get; set; } = true;
        public AccessibilitySettings Accessibility { get; set; } = new AccessibilitySettings();
    }

    /// <summary>
    /// Accessibility configuration settings
    /// </summary>
    public class AccessibilitySettings
    {
        public bool HighContrastMode { get; set; } = false;
        public bool ScreenReaderSupport { get; set; } = false;
        public double FontSizeMultiplier { get; set; } = 1.0;
        public bool ReducedMotion { get; set; } = false;
        public bool ShowCaptions { get; set; } = false;
        public string PreferredLanguage { get; set; } = "zh-CN";
    }

    /// <summary>
    /// Presentation usage statistics
    /// </summary>
    public class PresentationStatistics
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan TotalDuration => EndTime - StartTime;
        public int TotalSlideTransitions { get; set; }
        public Dictionary<int, TimeSpan> SlideViewTimes { get; set; } = new Dictionary<int, TimeSpan>();
        public Dictionary<int, int> SlideVisitCounts { get; set; } = new Dictionary<int, int>();
        public List<InteractionEvent> InteractionEvents { get; set; } = new List<InteractionEvent>();
        public List<string> AudioClipsPlayed { get; set; } = new List<string>();

        public void RecordSlideTransition(int slideIndex, DateTime timestamp)
        {
            TotalSlideTransitions++;
            
            if (!SlideVisitCounts.ContainsKey(slideIndex))
                SlideVisitCounts[slideIndex] = 0;
            
            SlideVisitCounts[slideIndex]++;
            
            InteractionEvents.Add(new InteractionEvent
            {
                Type = InteractionType.SlideTransition,
                Timestamp = timestamp,
                Data = new Dictionary<string, object> { { "slideIndex", slideIndex } }
            });
        }

        public void RecordAudioPlay(string audioId, DateTime timestamp)
        {
            AudioClipsPlayed.Add(audioId);
            InteractionEvents.Add(new InteractionEvent
            {
                Type = InteractionType.AudioPlay,
                Timestamp = timestamp,
                Data = new Dictionary<string, object> { { "audioId", audioId } }
            });
        }
    }

    /// <summary>
    /// Represents an interaction event during presentation
    /// </summary>
    public class InteractionEvent
    {
        public InteractionType Type { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Types of presentation modes
    /// </summary>
    public enum PresentationMode
    {
        SinglePresenter,
        DualPresenter,
        AudienceInteractive,
        Practice,
        Review
    }

    /// <summary>
    /// Types of user interactions
    /// </summary>
    public enum InteractionType
    {
        SlideTransition,
        AudioPlay,
        AudioStop,
        PresentationStart,
        PresentationEnd,
        PresentationPause,
        PresentationResume,
        UserInput,
        AIInteraction,
        NetworkEvent
    }
}
