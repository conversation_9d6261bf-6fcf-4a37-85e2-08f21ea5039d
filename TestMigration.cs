using System;
using System.IO;
using System.Threading.Tasks;
using PatrioticPresentationApp.Core.Services.ContentMigrationService;
using PatrioticPresentationApp.Core.Services.PresentationEngine;

namespace PatrioticPresentationApp.TestConsole
{
    /// <summary>
    /// Simple console application to test the HTML to .NET migration functionality
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== 家国情怀演示应用 - 内容迁移测试 ===");
            Console.WriteLine("Patriotic Presentation App - Content Migration Test");
            Console.WriteLine();

            try
            {
                // Test content migration
                await TestContentMigration();
                
                // Test presentation engine
                await TestPresentationEngine();
                
                Console.WriteLine();
                Console.WriteLine("✅ 所有测试完成！All tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败 Test failed: {ex.Message}");
                Console.WriteLine($"详细信息 Details: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出... Press any key to exit...");
            Console.ReadKey();
        }

        static async Task TestContentMigration()
        {
            Console.WriteLine("🔄 测试内容迁移功能 Testing content migration...");
            
            var migrationService = new ContentMigrationService();
            
            // Create sample HTML content
            var sampleHtml = @"
<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <title>家国情怀交互式PPT</title>
</head>
<body>
    <div class='presentation'>
        <div class='slide' data-slide='0' data-theme='patriotic-red'>
            <h1>家国情怀的历史渊源</h1>
            <div class='content'>
                <p>从古代的忠君爱国到现代的爱国主义，家国情怀承载着中华民族的精神传承。</p>
                <ul class='reveal-items'>
                    <li data-reveal='0'>古代文献中的家国观念</li>
                    <li data-reveal='1'>历史人物的爱国事迹</li>
                    <li data-reveal='2'>传统文化中的家国精神</li>
                    <li data-reveal='3'>现代转化与发展</li>
                </ul>
            </div>
            <audio src='audio/slide1-narration.mp3' data-autoplay='true'></audio>
        </div>
        
        <div class='slide' data-slide='1' data-theme='modern-blue'>
            <h1>新时代的家国担当</h1>
            <div class='content'>
                <p>在新时代背景下，家国情怀体现为对国家发展的责任担当和对民族复兴的使命感。</p>
                <ul class='reveal-items'>
                    <li data-reveal='0'>科技创新与国家实力</li>
                    <li data-reveal='1'>文化自信与民族精神</li>
                    <li data-reveal='2'>生态文明与可持续发展</li>
                    <li data-reveal='3'>国际合作与人类命运共同体</li>
                </ul>
                <div class='chart' data-type='bar' data-title='发展指标'>
                    <script>
                        var chartData = {
                            labels: ['科技', '文化', '生态', '国际'],
                            datasets: [{
                                data: [85, 78, 92, 88],
                                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0']
                            }]
                        };
                    </script>
                </div>
            </div>
            <audio src='audio/slide2-narration.mp3' data-autoplay='true'></audio>
        </div>
    </div>
</body>
</html>";

            // Test migration
            var presentation = await migrationService.MigrateFromHtmlContentAsync(sampleHtml);
            
            Console.WriteLine($"✅ 迁移成功 Migration successful!");
            Console.WriteLine($"   演示标题 Presentation title: {presentation.Title}");
            Console.WriteLine($"   幻灯片数量 Slide count: {presentation.Slides.Count}");
            Console.WriteLine($"   主题 Theme: {presentation.Theme}");
            Console.WriteLine($"   语言 Language: {presentation.Language}");
            
            // Display slide information
            for (int i = 0; i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                Console.WriteLine($"   幻灯片 Slide {i + 1}: {slide.Title}");
                Console.WriteLine($"     主题 Theme: {slide.Theme}");
                Console.WriteLine($"     显示项目 Reveal items: {slide.RevealItems.Count}");
                Console.WriteLine($"     音频剪辑 Audio clips: {slide.AudioClips.Count}");
                Console.WriteLine($"     图表 Charts: {slide.Charts.Count}");
            }
            
            Console.WriteLine();
        }

        static async Task TestPresentationEngine()
        {
            Console.WriteLine("🎯 测试演示引擎功能 Testing presentation engine...");
            
            var engine = new PresentationEngine();
            
            // Initialize engine
            await engine.InitializeAsync();
            Console.WriteLine($"✅ 引擎初始化 Engine initialized: {engine.IsInitialized}");
            
            // Create a test presentation file
            var testPresentation = CreateTestPresentationJson();
            var tempFile = Path.GetTempFileName();
            
            try
            {
                await File.WriteAllTextAsync(tempFile, testPresentation);
                
                // Load presentation
                await engine.LoadPresentationAsync(tempFile);
                Console.WriteLine($"✅ 演示加载 Presentation loaded: {engine.CurrentPresentation?.Title}");
                Console.WriteLine($"   当前幻灯片 Current slide: {engine.CurrentPresentation?.CurrentSlideIndex + 1}/{engine.CurrentPresentation?.TotalSlides}");
                
                // Test navigation
                Console.WriteLine($"   可以前进 Can go next: {engine.CanGoNext}");
                Console.WriteLine($"   可以后退 Can go previous: {engine.CanGoPrevious}");
                
                // Navigate to next slide
                if (engine.CanGoNext)
                {
                    await engine.NextSlideAsync();
                    Console.WriteLine($"✅ 前进到下一页 Advanced to next slide: {engine.CurrentPresentation?.CurrentSlideIndex + 1}");
                }
                
                // Test reveal items
                var currentSlide = engine.CurrentPresentation?.CurrentSlide;
                if (currentSlide?.RevealItems?.Count > 0)
                {
                    Console.WriteLine($"   显示项目数量 Reveal items count: {currentSlide.RevealItems.Count}");
                    await engine.RevealNextItemAsync();
                    Console.WriteLine($"✅ 显示下一项 Revealed next item: {currentSlide.CurrentRevealIndex}");
                }
                
                // Test statistics
                var stats = engine.GetStatistics();
                if (stats != null)
                {
                    Console.WriteLine($"   演示统计 Presentation statistics:");
                    Console.WriteLine($"     总时长 Total duration: {stats.TotalDuration}");
                    Console.WriteLine($"     查看次数 View count: {stats.ViewCount}");
                }
                
                // Test progress
                var progress = engine.GetProgress();
                Console.WriteLine($"   进度 Progress: {progress:F1}%");
                
            }
            finally
            {
                if (File.Exists(tempFile))
                    File.Delete(tempFile);
                    
                engine.Dispose();
            }
            
            Console.WriteLine();
        }

        static string CreateTestPresentationJson()
        {
            return @"{
  ""Id"": ""test-presentation-001"",
  ""Title"": ""家国情怀：从历史传承到时代担当"",
  ""Description"": ""爱国主义教育互动演示"",
  ""Author"": ""Educational Technology Solutions"",
  ""CreatedDate"": ""2024-01-15T10:00:00Z"",
  ""Version"": ""1.0.0"",
  ""Language"": ""zh-CN"",
  ""Theme"": ""Patriotic"",
  ""CurrentSlideIndex"": 0,
  ""Slides"": [
    {
      ""Id"": ""slide-001"",
      ""Index"": 0,
      ""Title"": ""家国情怀的历史渊源"",
      ""Content"": ""从古代的忠君爱国到现代的爱国主义，家国情怀承载着中华民族的精神传承。"",
      ""Theme"": ""PatrioticRed"",
      ""BackgroundImage"": ""slide1-bg.jpg"",
      ""RevealItems"": [
        {
          ""Id"": ""item-001"",
          ""Index"": 0,
          ""Content"": ""古代文献中的家国观念"",
          ""AnimationType"": ""FadeIn"",
          ""Delay"": ""00:00:00.200"",
          ""IsVisible"": false,
          ""IsRevealed"": false
        },
        {
          ""Id"": ""item-002"",
          ""Index"": 1,
          ""Content"": ""历史人物的爱国事迹"",
          ""AnimationType"": ""SlideInFromLeft"",
          ""Delay"": ""00:00:00.400"",
          ""IsVisible"": false,
          ""IsRevealed"": false
        }
      ],
      ""AudioClips"": [
        {
          ""Id"": ""audio-001"",
          ""FilePath"": ""audio/slide1-narration.mp3"",
          ""Title"": ""第一页旁白"",
          ""Duration"": ""00:00:30"",
          ""Volume"": 0.8,
          ""AutoPlay"": true
        }
      ],
      ""Charts"": [],
      ""CurrentRevealIndex"": 0
    },
    {
      ""Id"": ""slide-002"",
      ""Index"": 1,
      ""Title"": ""新时代的家国担当"",
      ""Content"": ""在新时代背景下，家国情怀体现为对国家发展的责任担当和对民族复兴的使命感。"",
      ""Theme"": ""ModernBlue"",
      ""BackgroundImage"": ""slide2-bg.jpg"",
      ""RevealItems"": [
        {
          ""Id"": ""item-003"",
          ""Index"": 0,
          ""Content"": ""科技创新与国家实力"",
          ""AnimationType"": ""FadeIn"",
          ""Delay"": ""00:00:00.200"",
          ""IsVisible"": false,
          ""IsRevealed"": false
        }
      ],
      ""AudioClips"": [],
      ""Charts"": [
        {
          ""Id"": ""chart-001"",
          ""Title"": ""发展指标"",
          ""Type"": ""Bar"",
          ""Data"": {
            ""labels"": [""科技"", ""文化"", ""生态"", ""国际""],
            ""values"": [85, 78, 92, 88]
          },
          ""Width"": 400,
          ""Height"": 300
        }
      ],
      ""CurrentRevealIndex"": 0
    }
  ],
  ""Settings"": {
    ""AutoPlay"": false,
    ""AutoPlayInterval"": ""00:00:05"",
    ""ShowProgressBar"": true,
    ""EnableKeyboardNavigation"": true,
    ""EnableMouseNavigation"": true,
    ""EnableTouchNavigation"": true,
    ""TransitionDuration"": ""00:00:00.800"",
    ""DefaultVolume"": 0.8
  }
}";
        }
    }
}";
        }
    }
}
