# 家国情怀交互式演示应用 (Patriotic Presentation App)

## 项目概述 (Project Overview)

这是一个基于 .NET Framework 的桌面应用程序，将原有的 HTML 交互式 PPT "家国情怀：从历史传承到时代担当" 转换为功能丰富的 Windows 桌面应用。该应用集成了 AI 教学助手、双人演示模式和实时观众互动功能。

This is a .NET Framework desktop application that transforms the existing HTML-based interactive PPT "Patriotic Sentiment: From Historical Heritage to Contemporary Responsibility" into a comprehensive Windows desktop application with AI-powered teaching assistance, dual-presenter mode, and real-time audience interaction capabilities.

## 核心功能 (Core Features)

### 🎯 演示引擎 (Presentation Engine)
- **幻灯片管理**: 12 页爱国主义主题内容
- **动画效果**: 渐进式显示、过渡动画、视觉特效
- **多媒体支持**: 图片、音频播放、图表可视化
- **导航控制**: 键盘、鼠标、触摸屏支持

### 🤖 AI 教学助手 (AI Teaching Assistant)
- **智能内容建议**: 基于当前幻灯片的内容推荐
- **问答辅助**: AI 驱动的问题回答和相关话题推荐
- **自适应学习路径**: 根据观众特征定制学习路径
- **语音识别与合成**: 支持语音交互和文本转语音

### 👥 双人演示模式 (Dual-Presenter Mode)
- **无缝协调**: 两位演示者之间的流畅切换
- **同步视图**: 实时同步演示内容和控制
- **角色管理**: 主演示者和副演示者角色切换
- **协作标注**: 共享标注和指针功能

### 📊 实时观众互动 (Real-time Audience Interaction)
- **投票系统**: 实时观众投票和结果展示
- **问答管理**: 观众提问队列和管理系统
- **反馈收集**: 实时情感反馈和参与度追踪
- **互动元素**: 响应式互动组件

### 📈 分析与报告 (Analytics & Reporting)
- **演示分析**: 详细的演示效果分析
- **观众洞察**: 观众参与度和行为分析
- **导出功能**: 多格式报告导出
- **实时指标**: 实时演示指标监控

## 技术架构 (Technical Architecture)

### 技术栈 (Technology Stack)
- **.NET Framework 4.7.2**: Windows 7-11 兼容性
- **WPF (Windows Presentation Foundation)**: 现代 UI 框架
- **MVVM 模式**: 模型-视图-视图模型架构
- **SignalR**: 实时通信
- **Entity Framework**: 数据访问层
- **NAudio**: 音频处理
- **OxyPlot/LiveCharts**: 图表可视化

### 项目结构 (Project Structure)
```
PatrioticPresentationApp/
├── PatrioticPresentationApp/          # 主 WPF 应用程序
│   ├── Views/                         # XAML 视图
│   ├── ViewModels/                    # 视图模型
│   ├── Controls/                      # 自定义控件
│   └── Resources/                     # 资源文件
├── PatrioticPresentationApp.Core/     # 核心业务逻辑
│   ├── Models/                        # 数据模型
│   ├── Services/                      # 业务服务
│   │   ├── PresentationEngine/        # 演示引擎
│   │   ├── AITeachingAssistant/       # AI 教学助手
│   │   ├── DualPresenterService/      # 双人演示服务
│   │   ├── AudienceInteractionService/# 观众互动服务
│   │   └── AnalyticsService/          # 分析服务
└── PatrioticPresentationApp.Tests/   # 单元测试
```

## 安装与部署 (Installation & Deployment)

### 系统要求 (System Requirements)
- **操作系统**: Windows 7 SP1 / Windows 8.1 / Windows 10 / Windows 11
- **.NET Framework**: 4.7.2 或更高版本
- **内存**: 最少 2GB RAM，推荐 4GB+
- **存储**: 500MB 可用磁盘空间
- **网络**: 可选，用于 AI 功能和观众互动

### 安装步骤 (Installation Steps)
1. 确保系统已安装 .NET Framework 4.7.2+
2. 下载应用程序安装包
3. 运行安装程序并按照向导完成安装
4. 首次启动时配置 AI 服务（可选）

## 使用指南 (User Guide)

### 基本操作 (Basic Operations)
- **← →**: 上一页/下一页
- **空格**: 下一页或显示下一项
- **P**: 播放/暂停自动播放
- **M**: 静音/取消静音
- **F11**: 全屏模式
- **Esc**: 退出全屏或关闭帮助

### 双人演示模式 (Dual-Presenter Mode)
1. 主演示者启动"双人演示模式"
2. 生成连接代码分享给副演示者
3. 副演示者使用代码连接
4. 通过控制面板管理演示权限

### AI 教学助手 (AI Teaching Assistant)
1. 点击 AI 助手按钮激活功能
2. 查看智能内容建议
3. 使用问答辅助功能
4. 配置自适应学习路径

### 观众互动 (Audience Interaction)
1. 启动观众互动服务
2. 生成观众加入码
3. 创建投票和问答环节
4. 实时查看参与数据

## 开发指南 (Development Guide)

### 构建项目 (Building the Project)

#### 前提条件 (Prerequisites)
- Visual Studio 2019 或更高版本 (推荐 Visual Studio 2022)
- .NET Framework 4.7.2 或更高版本
- Windows 7 SP1 / Windows 8.1 / Windows 10 / Windows 11

#### 构建步骤 (Build Steps)
```bash
# 1. 克隆仓库
git clone [repository-url]
cd PatrioticPresentationApp

# 2. 还原 NuGet 包
nuget restore PatrioticPresentationApp.sln

# 3. 使用 MSBuild 构建（命令行）
msbuild PatrioticPresentationApp.sln /p:Configuration=Release /p:Platform="Any CPU"

# 或者使用 Visual Studio
# 打开 PatrioticPresentationApp.sln
# 选择 Release 配置
# 构建 -> 重新生成解决方案
```

#### 快速测试 (Quick Test)
```bash
# 编译并运行测试控制台应用
csc /reference:PatrioticPresentationApp.Core.dll TestMigration.cs
TestMigration.exe
```

### 运行测试 (Running Tests)
```bash
# 运行所有测试
dotnet test PatrioticPresentationApp.Tests/

# 运行特定测试类别
dotnet test --filter Category=Unit
```

### 扩展功能 (Extending Features)
1. 实现 `IService` 接口创建新服务
2. 在 `App.xaml.cs` 中注册服务
3. 创建对应的 ViewModel 和 View
4. 添加必要的配置项

## 配置选项 (Configuration Options)

### App.config 主要配置项
```xml
<!-- AI 服务配置 -->
<add key="AIServiceEndpoint" value="your-ai-service-url" />
<add key="AIServiceKey" value="your-api-key" />

<!-- 演示设置 -->
<add key="DefaultSlideTransitionDuration" value="800" />
<add key="AutoPlayInterval" value="5000" />

<!-- 网络设置 -->
<add key="NetworkPort" value="8080" />
<add key="MaxAudienceConnections" value="100" />

<!-- 辅助功能 -->
<add key="EnableHighContrast" value="false" />
<add key="FontSizeMultiplier" value="1.0" />
```

## 故障排除 (Troubleshooting)

### 常见问题 (Common Issues)
1. **应用启动失败**: 检查 .NET Framework 版本
2. **AI 功能不可用**: 验证网络连接和 API 配置
3. **音频播放问题**: 检查音频驱动和文件格式
4. **网络连接失败**: 检查防火墙设置和端口配置

### 日志文件 (Log Files)
- 应用日志: `%APPDATA%/PatrioticPresentationApp/Logs/`
- 错误报告: `%TEMP%/PatrioticPresentationApp/CrashReports/`

## 贡献指南 (Contributing)

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证 (License)

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式 (Contact)

- 项目维护者: Educational Technology Solutions
- 邮箱: <EMAIL>
- 项目主页: [GitHub Repository URL]

## 更新日志 (Changelog)

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 完整的演示引擎实现
- AI 教学助手基础功能
- 双人演示模式
- 观众互动系统
- 分析和报告功能

---

**注意**: 这是一个教育技术项目，旨在提升爱国主义教育的互动性和效果。欢迎教育工作者和技术开发者共同参与改进。
