<Application x:Class="PatrioticPresentationApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <!-- Global Color Palette - Matching HTML Theme -->
        <SolidColorBrush x:Key="PrimaryBlueBrush" Color="#1E90FF"/>
        <SolidColorBrush x:Key="PrimaryRedBrush" Color="#FF4757"/>
        <SolidColorBrush x:Key="PrimaryGreenBrush" Color="#2ED573"/>
        <SolidColorBrush x:Key="GoldAccentBrush" Color="#FFD700"/>
        <SolidColorBrush x:Key="DarkBackgroundBrush" Color="#0D1A26"/>
        <SolidColorBrush x:Key="LightBackgroundBrush" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="TextPrimaryBrush" Color="#2D3436"/>
        <SolidColorBrush x:Key="TextSecondaryBrush" Color="#636E72"/>
        
        <!-- Global Font Settings -->
        <FontFamily x:Key="PrimaryFontFamily">Microsoft YaHei UI, Segoe UI, Arial</FontFamily>
        <FontFamily x:Key="ChineseFontFamily">Microsoft YaHei UI, SimSun</FontFamily>
        
        <!-- Animation Durations -->
        <Duration x:Key="FastTransition">0:0:0.3</Duration>
        <Duration x:Key="MediumTransition">0:0:0.8</Duration>
        <Duration x:Key="SlowTransition">0:0:1.2</Duration>
        
        <!-- Global Styles -->
        <Style x:Key="PatrioticButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#1E90FF" Offset="0"/>
                        <GradientStop Color="#0B6BCB" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="22" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#0B6BCB" Offset="0"/>
                                            <GradientStop Color="#1E90FF" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Slide Title Style -->
        <Style x:Key="SlideTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ChineseFontFamily}"/>
            <Setter Property="FontSize" Value="36"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>
        
        <!-- Slide Content Style -->
        <Style x:Key="SlideContentStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ChineseFontFamily}"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="28"/>
        </Style>
        
        <!-- Progress Bar Style -->
        <Style x:Key="PresentationProgressStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="6"/>
            <Setter Property="Background" Value="#33FFFFFF"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBlueBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ProgressBar">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3">
                            <Rectangle Name="PART_Track" 
                                     Fill="{TemplateBinding Foreground}" 
                                     HorizontalAlignment="Left"
                                     CornerRadius="3"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Application.Resources>
</Application>
