using System;
using System.Windows;
using System.Windows.Threading;
using PatrioticPresentationApp.Core.Services.PresentationEngine;
using PatrioticPresentationApp.Core.Services.AITeachingAssistant;
using PatrioticPresentationApp.Core.Services.DualPresenterService;
using PatrioticPresentationApp.Core.Services.AudienceInteractionService;
using PatrioticPresentationApp.Core.Services.AnalyticsService;

namespace PatrioticPresentationApp
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// Main application entry point with dependency injection and service initialization
    /// </summary>
    public partial class App : Application
    {
        // Core Services
        public static IPresentationEngine PresentationEngine { get; private set; }
        public static IAITeachingAssistant AIAssistant { get; private set; }
        public static IDualPresenterService DualPresenterService { get; private set; }
        public static IAudienceInteractionService AudienceService { get; private set; }
        public static IAnalyticsService AnalyticsService { get; private set; }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Initialize core services
            InitializeServices();

            // Set up global exception handling
            SetupExceptionHandling();

            // Load application configuration
            LoadConfiguration();

            // Initialize presentation content
            InitializePresentationContent();
        }

        private void InitializeServices()
        {
            try
            {
                // Initialize Presentation Engine
                PresentationEngine = new PresentationEngine();

                // Initialize AI Teaching Assistant
                AIAssistant = new AITeachingAssistant();

                // Initialize Dual Presenter Service
                DualPresenterService = new DualPresenterService();

                // Initialize Audience Interaction Service
                AudienceService = new AudienceInteractionService();

                // Initialize Analytics Service
                AnalyticsService = new AnalyticsService();

                // Log successful initialization
                System.Diagnostics.Debug.WriteLine("All services initialized successfully.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to initialize application services: {ex.Message}", 
                              "Initialization Error", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        private void SetupExceptionHandling()
        {
            // Handle unhandled exceptions in the main UI thread
            DispatcherUnhandledException += (sender, e) =>
            {
                LogException(e.Exception);
                MessageBox.Show($"An unexpected error occurred: {e.Exception.Message}", 
                              "Application Error", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
                e.Handled = true;
            };

            // Handle unhandled exceptions in background threads
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                LogException(e.ExceptionObject as Exception);
            };
        }

        private void LoadConfiguration()
        {
            try
            {
                // Load configuration from App.config
                var config = System.Configuration.ConfigurationManager.AppSettings;
                
                // Apply configuration to services
                PresentationEngine?.LoadConfiguration(config);
                AIAssistant?.LoadConfiguration(config);
                DualPresenterService?.LoadConfiguration(config);
                AudienceService?.LoadConfiguration(config);
                AnalyticsService?.LoadConfiguration(config);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Configuration loading error: {ex.Message}");
            }
        }

        private void InitializePresentationContent()
        {
            try
            {
                // Load the patriotic presentation content
                PresentationEngine?.LoadPresentationContent("Resources/Slides/PatrioticPresentation.json");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Content loading error: {ex.Message}");
            }
        }

        private void LogException(Exception exception)
        {
            if (exception != null)
            {
                // Log to debug output
                System.Diagnostics.Debug.WriteLine($"Exception: {exception}");
                
                // Log to analytics service if available
                AnalyticsService?.LogError(exception);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // Cleanup services
                PresentationEngine?.Dispose();
                AIAssistant?.Dispose();
                DualPresenterService?.Dispose();
                AudienceService?.Dispose();
                AnalyticsService?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Cleanup error: {ex.Message}");
            }

            base.OnExit(e);
        }
    }
}
