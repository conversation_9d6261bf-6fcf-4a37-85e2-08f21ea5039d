using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using PatrioticPresentationApp.ViewModels;
using PatrioticPresentationApp.Core.Models;

namespace PatrioticPresentationApp.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// Main presentation window with slide display and controls
    /// </summary>
    public partial class MainWindow : Window
    {
        private MainViewModel _viewModel;
        private bool _isFullscreen = false;
        private WindowState _previousWindowState;
        private WindowStyle _previousWindowStyle;

        public MainWindow()
        {
            InitializeComponent();
            InitializeViewModel();
            SetupEventHandlers();
            SetupKeyboardShortcuts();
        }

        private void InitializeViewModel()
        {
            _viewModel = new MainViewModel();
            DataContext = _viewModel;
            
            // Subscribe to view model events
            _viewModel.SlideChanged += OnSlideChanged;
            _viewModel.PresentationStateChanged += OnPresentationStateChanged;
            _viewModel.ErrorOccurred += OnErrorOccurred;
        }

        private void SetupEventHandlers()
        {
            // Window events
            Loaded += MainWindow_Loaded;
            Closing += MainWindow_Closing;
            
            // Mouse events for slide navigation
            SlideContainer.MouseLeftButtonDown += SlideContainer_MouseLeftButtonDown;
            
            // Progress bar click handling
            PresentationProgress.MouseLeftButtonDown += PresentationProgress_MouseLeftButtonDown;
        }

        private void SetupKeyboardShortcuts()
        {
            // Global keyboard shortcuts
            KeyDown += (sender, e) =>
            {
                switch (e.Key)
                {
                    case Key.Left:
                        _viewModel.PreviousSlideCommand.Execute(null);
                        e.Handled = true;
                        break;
                    case Key.Right:
                    case Key.Space:
                        _viewModel.NextSlideCommand.Execute(null);
                        e.Handled = true;
                        break;
                    case Key.P:
                        _viewModel.TogglePlayPauseCommand.Execute(null);
                        e.Handled = true;
                        break;
                    case Key.M:
                        _viewModel.ToggleAudioCommand.Execute(null);
                        e.Handled = true;
                        break;
                    case Key.F11:
                        ToggleFullscreen();
                        e.Handled = true;
                        break;
                    case Key.Escape:
                        if (_isFullscreen)
                        {
                            ExitFullscreen();
                        }
                        else if (HelpOverlay.Visibility == Visibility.Visible)
                        {
                            HelpOverlay.Visibility = Visibility.Collapsed;
                        }
                        e.Handled = true;
                        break;
                    case Key.F1:
                        ShowHelp();
                        e.Handled = true;
                        break;
                }
            };
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadingIndicator.Visibility = Visibility.Visible;
                await _viewModel.InitializeAsync();
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                LoadingIndicator.Visibility = Visibility.Collapsed;
                MessageBox.Show($"初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            _viewModel?.Dispose();
        }

        // Button Event Handlers
        private void PreviousButton_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.PreviousSlideCommand.Execute(null);
        }

        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.NextSlideCommand.Execute(null);
        }

        private void PlayPauseButton_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.TogglePlayPauseCommand.Execute(null);
            UpdatePlayPauseIcon();
        }

        private void AudioButton_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.ToggleAudioCommand.Execute(null);
            UpdateAudioIcon();
        }

        private void AIAssistantButton_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.ShowAIAssistantCommand.Execute(null);
        }

        private void DualPresenterButton_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.ToggleDualPresenterModeCommand.Execute(null);
        }

        private void FullscreenButton_Click(object sender, RoutedEventArgs e)
        {
            ToggleFullscreen();
        }

        private void CloseHelp_Click(object sender, RoutedEventArgs e)
        {
            HelpOverlay.Visibility = Visibility.Collapsed;
        }

        // Mouse Event Handlers
        private void SlideContainer_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // Click on slide to advance
            _viewModel.NextSlideCommand.Execute(null);
        }

        private void PresentationProgress_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // Calculate clicked position and jump to slide
            var progressBar = sender as ProgressBar;
            if (progressBar != null)
            {
                var position = e.GetPosition(progressBar);
                var percentage = position.X / progressBar.ActualWidth;
                var slideIndex = (int)(percentage * (_viewModel.TotalSlides - 1));
                _viewModel.GoToSlideCommand.Execute(slideIndex);
            }
        }

        // ViewModel Event Handlers
        private void OnSlideChanged(object sender, SlideChangedEventArgs e)
        {
            // Animate slide transition
            AnimateSlideTransition(e.NewSlideIndex > e.OldSlideIndex);
        }

        private void OnPresentationStateChanged(object sender, PresentationEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                switch (e.Type)
                {
                    case PresentationEventType.Started:
                        UpdatePlayPauseIcon();
                        break;
                    case PresentationEventType.Paused:
                    case PresentationEventType.Stopped:
                        UpdatePlayPauseIcon();
                        break;
                    case PresentationEventType.Error:
                        MessageBox.Show(e.Message, "演示错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                        break;
                }
            });
        }

        private void OnErrorOccurred(object sender, ErrorEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                MessageBox.Show(e.ErrorMessage, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            });
        }

        // UI Update Methods
        private void UpdatePlayPauseIcon()
        {
            if (_viewModel.IsPlaying)
            {
                // Pause icon
                PlayPauseIcon.Data = Geometry.Parse("M6,4V20H8V4H6M10,4V20H12V4H10Z");
            }
            else
            {
                // Play icon
                PlayPauseIcon.Data = Geometry.Parse("M8,5V19L19,12M6,4V20H8V4H6M10,4V20H12V4H10Z");
            }
        }

        private void UpdateAudioIcon()
        {
            if (_viewModel.IsAudioMuted)
            {
                // Muted icon
                AudioIcon.Data = Geometry.Parse("M12,4L9.91,6.09L12,8.18M4.27,3L3,4.27L7.73,9H3V15H7L12,20V13.27L16.25,17.53C15.58,18.04 14.83,18.46 14,18.7V20.77C15.38,20.45 16.63,19.82 17.68,18.96L19.73,21L21,19.73L12,10.73M19,12C19,12.94 18.8,13.82 18.46,14.64L19.97,16.15C20.62,14.91 21,13.5 21,12C21,7.72 18,4.14 14,3.23V5.29C16.89,6.15 19,8.83 19,12M16.5,12C16.5,10.23 15.5,8.71 14,7.97V10.18L16.45,12.63C16.5,12.43 16.5,12.21 16.5,12Z");
            }
            else
            {
                // Unmuted icon
                AudioIcon.Data = Geometry.Parse("M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18.01,19.86 21,16.28 21,12C21,7.72 18.01,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z");
            }
        }

        // Animation Methods
        private void AnimateSlideTransition(bool isForward)
        {
            var storyboard = isForward ? 
                FindResource("SlideInFromRight") as Storyboard : 
                FindResource("FadeIn") as Storyboard;

            if (storyboard != null)
            {
                Storyboard.SetTarget(storyboard, SlideContentPresenter);
                storyboard.Begin();
            }
        }

        // Fullscreen Methods
        private void ToggleFullscreen()
        {
            if (_isFullscreen)
            {
                ExitFullscreen();
            }
            else
            {
                EnterFullscreen();
            }
        }

        private void EnterFullscreen()
        {
            _previousWindowState = WindowState;
            _previousWindowStyle = WindowStyle;
            
            WindowStyle = WindowStyle.None;
            WindowState = WindowState.Maximized;
            _isFullscreen = true;
        }

        private void ExitFullscreen()
        {
            WindowStyle = _previousWindowStyle;
            WindowState = _previousWindowState;
            _isFullscreen = false;
        }

        private void ShowHelp()
        {
            HelpOverlay.Visibility = Visibility.Visible;
        }
    }

    // Custom EventArgs for error handling
    public class ErrorEventArgs : EventArgs
    {
        public string ErrorMessage { get; }
        public Exception Exception { get; }

        public ErrorEventArgs(string message, Exception exception = null)
        {
            ErrorMessage = message;
            Exception = exception;
        }
    }
}
