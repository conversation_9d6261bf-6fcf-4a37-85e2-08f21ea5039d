using System;
using System.Collections.Generic;
using System.Linq;

namespace PatrioticPresentationApp.Core.Models
{
    /// <summary>
    /// Analytics configuration settings
    /// </summary>
    public class AnalyticsConfiguration
    {
        public bool IsEnabled { get; set; } = true;
        public string ExportPath { get; set; } = "./Exports/";
        public TimeSpan DataRetentionPeriod { get; set; } = TimeSpan.FromDays(90);
        public bool CollectPersonalData { get; set; } = false;
        public bool EnableRealTimeAnalytics { get; set; } = true;
        public int BatchSize { get; set; } = 100;
        public TimeSpan FlushInterval { get; set; } = TimeSpan.FromMinutes(5);
    }

    /// <summary>
    /// Analytics event for data collection
    /// </summary>
    public class AnalyticsEvent
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public AnalyticsEventType Type { get; set; }
        public string PresentationId { get; set; }
        public string SessionId { get; set; }
        public string AudienceId { get; set; }
        public int? SlideIndex { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        public string UserAgent { get; set; }
        public string IPAddress { get; set; }
    }

    /// <summary>
    /// Presentation session for tracking
    /// </summary>
    public class PresentationSession
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PresentationId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;
        public SessionStatus Status { get; set; }
        public int AudienceCount { get; set; }
        public List<string> ConnectedAudienceIds { get; set; } = new List<string>();
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Engagement data for audience members
    /// </summary>
    public class EngagementData
    {
        public string AudienceId { get; set; }
        public double Score { get; set; }
        public int InteractionCount { get; set; }
        public double AttentionLevel { get; set; }
        public TimeSpan ActiveTime { get; set; }
        public DateTime LastActivity { get; set; }
        public List<string> InteractionTypes { get; set; } = new List<string>();
    }

    /// <summary>
    /// Presentation analytics summary
    /// </summary>
    public class PresentationAnalytics
    {
        public string PresentationId { get; set; }
        public int TotalViews { get; set; }
        public TimeSpan AverageViewDuration { get; set; }
        public int TotalSlideViews { get; set; }
        public double EngagementScore { get; set; }
        public double CompletionRate { get; set; }
        public int UniqueViewers { get; set; }
        public DateTime GeneratedTime { get; set; }
        public Dictionary<int, SlideAnalytics> SlideAnalytics { get; set; } = new Dictionary<int, SlideAnalytics>();
    }

    /// <summary>
    /// Individual slide analytics
    /// </summary>
    public class SlideAnalytics
    {
        public string PresentationId { get; set; }
        public int SlideIndex { get; set; }
        public int ViewCount { get; set; }
        public TimeSpan AverageViewTime { get; set; }
        public TimeSpan TotalViewTime { get; set; }
        public double EngagementLevel { get; set; }
        public int InteractionCount { get; set; }
        public int SkipCount { get; set; }
        public double RetentionRate { get; set; }
        public List<InteractionSummary> Interactions { get; set; } = new List<InteractionSummary>();
    }

    /// <summary>
    /// Audience analytics summary
    /// </summary>
    public class AudienceAnalytics
    {
        public string SessionId { get; set; }
        public int TotalAudience { get; set; }
        public int PeakAudience { get; set; }
        public TimeSpan AverageSessionDuration { get; set; }
        public double AverageEngagementScore { get; set; }
        public int TotalInteractions { get; set; }
        public Dictionary<string, int> InteractionTypes { get; set; } = new Dictionary<string, int>();
        public List<AudienceSegment> Segments { get; set; } = new List<AudienceSegment>();
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// Engagement analytics
    /// </summary>
    public class EngagementAnalytics
    {
        public string PresentationId { get; set; }
        public double OverallEngagementScore { get; set; }
        public Dictionary<int, double> SlideEngagementScores { get; set; } = new Dictionary<int, double>();
        public List<EngagementTrend> Trends { get; set; } = new List<EngagementTrend>();
        public List<EngagementPeak> Peaks { get; set; } = new List<EngagementPeak>();
        public TimeSpan HighEngagementDuration { get; set; }
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// Performance analytics
    /// </summary>
    public class PerformanceAnalytics
    {
        public string PresentationId { get; set; }
        public TimeSpan AverageLoadTime { get; set; }
        public TimeSpan AverageTransitionTime { get; set; }
        public int ErrorCount { get; set; }
        public double SystemResourceUsage { get; set; }
        public List<PerformanceMetric> Metrics { get; set; } = new List<PerformanceMetric>();
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// Comprehensive analytics report
    /// </summary>
    public class AnalyticsReport
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PresentationId { get; set; }
        public ReportType ReportType { get; set; }
        public DateTime GeneratedTime { get; set; }
        public PresentationAnalytics PresentationAnalytics { get; set; }
        public AudienceAnalytics AudienceAnalytics { get; set; }
        public EngagementAnalytics EngagementAnalytics { get; set; }
        public PerformanceAnalytics PerformanceAnalytics { get; set; }
        public List<AnalyticsInsight> Insights { get; set; } = new List<AnalyticsInsight>();
        public List<Recommendation> Recommendations { get; set; } = new List<Recommendation>();
    }

    /// <summary>
    /// Real-time metrics
    /// </summary>
    public class RealTimeMetrics
    {
        public int ActiveSessions { get; set; }
        public int TotalConnectedAudience { get; set; }
        public double CurrentEngagementLevel { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public int ActivePolls { get; set; }
        public int PendingQuestions { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Analytics insight
    /// </summary>
    public class AnalyticsInsight
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public InsightType Type { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public double Impact { get; set; }
        public double Confidence { get; set; }
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// Recommendation based on analytics
    /// </summary>
    public class Recommendation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public RecommendationType Type { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public int Priority { get; set; }
        public double ExpectedImpact { get; set; }
        public string ActionRequired { get; set; }
        public DateTime GeneratedTime { get; set; }
    }

    // Supporting classes
    public class InteractionSummary
    {
        public InteractionType Type { get; set; }
        public int Count { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double SuccessRate { get; set; }
    }

    public class AudienceSegment
    {
        public string Name { get; set; }
        public int Count { get; set; }
        public double EngagementScore { get; set; }
        public List<string> Characteristics { get; set; } = new List<string>();
    }

    public class EngagementTrend
    {
        public DateTime Timestamp { get; set; }
        public double EngagementScore { get; set; }
        public int AudienceCount { get; set; }
        public string Context { get; set; }
    }

    public class EngagementPeak
    {
        public DateTime Timestamp { get; set; }
        public double PeakScore { get; set; }
        public int SlideIndex { get; set; }
        public string Trigger { get; set; }
        public TimeSpan Duration { get; set; }
    }

    public class PerformanceMetric
    {
        public string Name { get; set; }
        public double Value { get; set; }
        public string Unit { get; set; }
        public DateTime Timestamp { get; set; }
        public string Category { get; set; }
    }

    public class ComparisonReport
    {
        public List<string> SessionIds { get; set; } = new List<string>();
        public Dictionary<string, PresentationAnalytics> SessionAnalytics { get; set; } = new Dictionary<string, PresentationAnalytics>();
        public List<ComparisonInsight> Insights { get; set; } = new List<ComparisonInsight>();
        public DateTime GeneratedTime { get; set; }
    }

    public class ComparisonInsight
    {
        public string Metric { get; set; }
        public string BestSession { get; set; }
        public string WorstSession { get; set; }
        public double Difference { get; set; }
        public string Analysis { get; set; }
    }

    public class LiveEngagementData
    {
        public DateTime Timestamp { get; set; }
        public double EngagementScore { get; set; }
        public int ActiveUsers { get; set; }
        public int CurrentSlide { get; set; }
        public Dictionary<string, int> ReactionCounts { get; set; } = new Dictionary<string, int>();
    }

    public class AlertData
    {
        public List<Alert> Alerts { get; set; } = new List<Alert>();
        public DateTime CheckTime { get; set; }
    }

    public class Alert
    {
        public AlertType Type { get; set; }
        public string Message { get; set; }
        public AlertSeverity Severity { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
    }

    public class DataExportOptions
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<string> PresentationIds { get; set; } = new List<string>();
        public List<AnalyticsEventType> EventTypes { get; set; } = new List<AnalyticsEventType>();
        public bool IncludePersonalData { get; set; } = false;
        public ExportFormat Format { get; set; } = ExportFormat.JSON;
    }

    public class DataSummary
    {
        public int TotalEvents { get; set; }
        public int TotalSessions { get; set; }
        public int TotalPresentations { get; set; }
        public DateTime OldestEvent { get; set; }
        public DateTime NewestEvent { get; set; }
        public long StorageSize { get; set; }
        public Dictionary<AnalyticsEventType, int> EventTypeCounts { get; set; } = new Dictionary<AnalyticsEventType, int>();
    }

    // Enumerations
    public enum AnalyticsEventType
    {
        PresentationStart,
        PresentationEnd,
        SlideView,
        UserInteraction,
        AudienceEngagement,
        PollResponse,
        QuestionSubmitted,
        FeedbackReceived,
        Error,
        PerformanceMetric
    }

    public enum SessionStatus
    {
        Active,
        Completed,
        Abandoned,
        Error
    }

    public enum ReportType
    {
        Summary,
        Detailed,
        Comprehensive,
        Comparison,
        RealTime
    }

    public enum ReportFormat
    {
        PDF,
        Excel,
        JSON,
        CSV,
        HTML
    }

    public enum InsightType
    {
        Engagement,
        Performance,
        Audience,
        Content,
        Technical
    }

    public enum RecommendationType
    {
        ContentImprovement,
        EngagementBoost,
        PerformanceOptimization,
        AudienceTargeting,
        TechnicalFix
    }

    public enum AlertType
    {
        LowEngagement,
        HighErrorRate,
        PerformanceIssue,
        AudienceDropoff,
        SystemAlert
    }

    public enum AlertSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    public enum ExportFormat
    {
        JSON,
        CSV,
        Excel,
        XML
    }

    // Event Arguments
    public class AnalyticsEventArgs : EventArgs
    {
        public AnalyticsEvent Event { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class ReportGeneratedEventArgs : EventArgs
    {
        public AnalyticsReport Report { get; set; }
        public DateTime GeneratedTime { get; set; }
    }
}
