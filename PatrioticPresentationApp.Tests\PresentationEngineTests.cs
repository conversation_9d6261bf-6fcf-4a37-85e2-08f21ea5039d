using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using PatrioticPresentationApp.Core.Models;
using PatrioticPresentationApp.Core.Services.PresentationEngine;
using PatrioticPresentationApp.Core.Services.ContentMigrationService;

namespace PatrioticPresentationApp.Tests
{
    [TestClass]
    public class PresentationEngineTests
    {
        private IPresentationEngine _presentationEngine;
        private IContentMigrationService _migrationService;

        [TestInitialize]
        public void Setup()
        {
            _presentationEngine = new PresentationEngine();
            _migrationService = new ContentMigrationService();
        }

        [TestMethod]
        public async Task InitializeAsync_ShouldSetIsInitializedToTrue()
        {
            // Act
            await _presentationEngine.InitializeAsync();

            // Assert
            Assert.IsTrue(_presentationEngine.IsInitialized);
        }

        [TestMethod]
        public async Task LoadPresentationAsync_WithValidFile_ShouldLoadSuccessfully()
        {
            // Arrange
            await _presentationEngine.InitializeAsync();
            var testPresentation = CreateTestPresentation();
            var tempFile = System.IO.Path.GetTempFileName();
            
            try
            {
                // Save test presentation to temp file
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(testPresentation);
                await System.IO.File.WriteAllTextAsync(tempFile, json);

                // Act
                await _presentationEngine.LoadPresentationAsync(tempFile);

                // Assert
                Assert.IsNotNull(_presentationEngine.CurrentPresentation);
                Assert.AreEqual(testPresentation.Title, _presentationEngine.CurrentPresentation.Title);
            }
            finally
            {
                if (System.IO.File.Exists(tempFile))
                    System.IO.File.Delete(tempFile);
            }
        }

        [TestMethod]
        public async Task NextSlideAsync_WhenCanGoNext_ShouldAdvanceSlide()
        {
            // Arrange
            await _presentationEngine.InitializeAsync();
            var testPresentation = CreateTestPresentation();
            var tempFile = System.IO.Path.GetTempFileName();
            
            try
            {
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(testPresentation);
                await System.IO.File.WriteAllTextAsync(tempFile, json);
                await _presentationEngine.LoadPresentationAsync(tempFile);

                var initialIndex = _presentationEngine.CurrentPresentation.CurrentSlideIndex;

                // Act
                await _presentationEngine.NextSlideAsync();

                // Assert
                Assert.AreEqual(initialIndex + 1, _presentationEngine.CurrentPresentation.CurrentSlideIndex);
            }
            finally
            {
                if (System.IO.File.Exists(tempFile))
                    System.IO.File.Delete(tempFile);
            }
        }

        [TestMethod]
        public async Task PreviousSlideAsync_WhenCanGoPrevious_ShouldGoBackSlide()
        {
            // Arrange
            await _presentationEngine.InitializeAsync();
            var testPresentation = CreateTestPresentation();
            var tempFile = System.IO.Path.GetTempFileName();
            
            try
            {
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(testPresentation);
                await System.IO.File.WriteAllTextAsync(tempFile, json);
                await _presentationEngine.LoadPresentationAsync(tempFile);

                // Move to second slide first
                await _presentationEngine.NextSlideAsync();
                var currentIndex = _presentationEngine.CurrentPresentation.CurrentSlideIndex;

                // Act
                await _presentationEngine.PreviousSlideAsync();

                // Assert
                Assert.AreEqual(currentIndex - 1, _presentationEngine.CurrentPresentation.CurrentSlideIndex);
            }
            finally
            {
                if (System.IO.File.Exists(tempFile))
                    System.IO.File.Delete(tempFile);
            }
        }

        [TestMethod]
        public async Task RevealNextItemAsync_WithRevealItems_ShouldRevealItem()
        {
            // Arrange
            await _presentationEngine.InitializeAsync();
            var testPresentation = CreateTestPresentation();
            var tempFile = System.IO.Path.GetTempFileName();
            
            try
            {
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(testPresentation);
                await System.IO.File.WriteAllTextAsync(tempFile, json);
                await _presentationEngine.LoadPresentationAsync(tempFile);

                var currentSlide = _presentationEngine.CurrentPresentation.CurrentSlide;
                var initialRevealIndex = currentSlide.CurrentRevealIndex;

                // Act
                await _presentationEngine.RevealNextItemAsync();

                // Assert
                Assert.AreEqual(initialRevealIndex + 1, currentSlide.CurrentRevealIndex);
                Assert.IsTrue(currentSlide.RevealItems[initialRevealIndex].IsVisible);
            }
            finally
            {
                if (System.IO.File.Exists(tempFile))
                    System.IO.File.Delete(tempFile);
            }
        }

        [TestMethod]
        public async Task MigrateFromHtmlContentAsync_WithValidHtml_ShouldCreatePresentation()
        {
            // Arrange
            var htmlContent = @"
                <html>
                    <head><title>家国情怀交互式PPT</title></head>
                    <body>
                        <div class='slide' data-slide='0'>
                            <h1>家国情怀的历史渊源</h1>
                            <p>从古代的忠君爱国到现代的爱国主义</p>
                        </div>
                    </body>
                </html>";

            // Act
            var presentation = await _migrationService.MigrateFromHtmlContentAsync(htmlContent);

            // Assert
            Assert.IsNotNull(presentation);
            Assert.AreEqual("家国情怀交互式PPT", presentation.Title);
            Assert.IsTrue(presentation.Slides.Count > 0);
        }

        [TestMethod]
        public void CanGoNext_WithMultipleSlides_ShouldReturnCorrectValue()
        {
            // Arrange
            var presentation = CreateTestPresentation();
            presentation.CurrentSlideIndex = 0;

            // Act & Assert
            Assert.IsTrue(presentation.CanGoNext());

            // Move to last slide
            presentation.CurrentSlideIndex = presentation.TotalSlides - 1;
            Assert.IsFalse(presentation.CanGoNext());
        }

        [TestMethod]
        public void CanGoPrevious_WithMultipleSlides_ShouldReturnCorrectValue()
        {
            // Arrange
            var presentation = CreateTestPresentation();
            presentation.CurrentSlideIndex = 0;

            // Act & Assert
            Assert.IsFalse(presentation.CanGoPrevious());

            // Move to second slide
            presentation.CurrentSlideIndex = 1;
            Assert.IsTrue(presentation.CanGoPrevious());
        }

        [TestMethod]
        public void GetProgress_ShouldReturnCorrectPercentage()
        {
            // Arrange
            var presentation = CreateTestPresentation();
            presentation.CurrentSlideIndex = 1; // Second slide of 3 slides

            // Act
            var progress = presentation.Progress;

            // Assert
            Assert.AreEqual(50.0, progress, 0.1); // 1/2 * 100 = 50%
        }

        private PresentationModel CreateTestPresentation()
        {
            return new PresentationModel
            {
                Id = Guid.NewGuid().ToString(),
                Title = "Test Patriotic Presentation",
                Description = "Test presentation for unit testing",
                Author = "Test Author",
                CreatedDate = DateTime.Now,
                Version = "1.0.0",
                Language = "zh-CN",
                Theme = PresentationTheme.Patriotic,
                CurrentSlideIndex = 0,
                Slides = new System.Collections.Generic.List<SlideModel>
                {
                    new SlideModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Index = 0,
                        Title = "Test Slide 1",
                        Content = "This is the first test slide",
                        Theme = SlideTheme.PatrioticRed,
                        RevealItems = new System.Collections.Generic.List<RevealItemModel>
                        {
                            new RevealItemModel
                            {
                                Id = Guid.NewGuid().ToString(),
                                Index = 0,
                                Content = "First reveal item",
                                AnimationType = AnimationType.FadeIn,
                                IsVisible = false,
                                IsRevealed = false
                            },
                            new RevealItemModel
                            {
                                Id = Guid.NewGuid().ToString(),
                                Index = 1,
                                Content = "Second reveal item",
                                AnimationType = AnimationType.SlideInFromLeft,
                                IsVisible = false,
                                IsRevealed = false
                            }
                        },
                        CurrentRevealIndex = 0
                    },
                    new SlideModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Index = 1,
                        Title = "Test Slide 2",
                        Content = "This is the second test slide",
                        Theme = SlideTheme.ModernBlue,
                        RevealItems = new System.Collections.Generic.List<RevealItemModel>(),
                        CurrentRevealIndex = 0
                    },
                    new SlideModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Index = 2,
                        Title = "Test Slide 3",
                        Content = "This is the third test slide",
                        Theme = SlideTheme.EducationGreen,
                        RevealItems = new System.Collections.Generic.List<RevealItemModel>(),
                        CurrentRevealIndex = 0
                    }
                },
                Settings = new PresentationSettings
                {
                    AutoPlay = false,
                    AutoPlayInterval = TimeSpan.FromSeconds(5),
                    ShowProgressBar = true,
                    EnableKeyboardNavigation = true,
                    TransitionDuration = TimeSpan.FromMilliseconds(800)
                }
            };
        }

        [TestCleanup]
        public void Cleanup()
        {
            _presentationEngine?.Dispose();
        }
    }
}
