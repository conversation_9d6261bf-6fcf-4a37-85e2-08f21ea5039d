using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Input;
using PatrioticPresentationApp.Core.Models;
using PatrioticPresentationApp.Core.Services.PresentationEngine;
using PatrioticPresentationApp.Core.Services.AITeachingAssistant;
using PatrioticPresentationApp.Core.Services.DualPresenterService;
using PatrioticPresentationApp.Core.Services.AudienceInteractionService;

namespace PatrioticPresentationApp.ViewModels
{
    /// <summary>
    /// Main ViewModel for the presentation application
    /// Implements MVVM pattern for UI data binding and command handling
    /// </summary>
    public class MainViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly IPresentationEngine _presentationEngine;
        private readonly IAITeachingAssistant _aiAssistant;
        private readonly IDualPresenterService _dualPresenterService;
        private readonly IAudienceInteractionService _audienceService;

        private PresentationModel _currentPresentation;
        private bool _isPlaying;
        private bool _isAudioMuted;
        private bool _isDualPresenterMode;
        private string _statusMessage;

        // Events
        public event EventHandler<SlideChangedEventArgs> SlideChanged;
        public event EventHandler<PresentationEventArgs> PresentationStateChanged;
        public event EventHandler<ErrorEventArgs> ErrorOccurred;
        public event PropertyChangedEventHandler PropertyChanged;

        // Properties
        public PresentationModel CurrentPresentation
        {
            get => _currentPresentation;
            private set
            {
                _currentPresentation = value;
                OnPropertyChanged(nameof(CurrentPresentation));
                OnPropertyChanged(nameof(CurrentSlide));
                OnPropertyChanged(nameof(TotalSlides));
                OnPropertyChanged(nameof(Progress));
                OnPropertyChanged(nameof(SlideInfoText));
            }
        }

        public SlideModel CurrentSlide => CurrentPresentation?.CurrentSlide;

        public int TotalSlides => CurrentPresentation?.TotalSlides ?? 0;

        public double Progress => CurrentPresentation?.Progress ?? 0;

        public bool IsPlaying
        {
            get => _isPlaying;
            private set
            {
                _isPlaying = value;
                OnPropertyChanged(nameof(IsPlaying));
            }
        }

        public bool IsAudioMuted
        {
            get => _isAudioMuted;
            private set
            {
                _isAudioMuted = value;
                OnPropertyChanged(nameof(IsAudioMuted));
            }
        }

        public bool IsDualPresenterMode
        {
            get => _isDualPresenterMode;
            private set
            {
                _isDualPresenterMode = value;
                OnPropertyChanged(nameof(IsDualPresenterMode));
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            private set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        public string SlideInfoText => CurrentPresentation != null ? 
            $"第 {CurrentPresentation.CurrentSlideIndex + 1} 页 / 共 {TotalSlides} 页" : 
            "未加载演示";

        // Commands
        public ICommand NextSlideCommand { get; private set; }
        public ICommand PreviousSlideCommand { get; private set; }
        public ICommand GoToSlideCommand { get; private set; }
        public ICommand TogglePlayPauseCommand { get; private set; }
        public ICommand ToggleAudioCommand { get; private set; }
        public ICommand ShowAIAssistantCommand { get; private set; }
        public ICommand ToggleDualPresenterModeCommand { get; private set; }
        public ICommand StartPresentationCommand { get; private set; }
        public ICommand StopPresentationCommand { get; private set; }

        public MainViewModel()
        {
            // Get services from App
            _presentationEngine = App.PresentationEngine;
            _aiAssistant = App.AIAssistant;
            _dualPresenterService = App.DualPresenterService;
            _audienceService = App.AudienceService;

            InitializeCommands();
            SetupEventHandlers();
        }

        private void InitializeCommands()
        {
            NextSlideCommand = new RelayCommand(async () => await NextSlideAsync(), () => CanGoNext());
            PreviousSlideCommand = new RelayCommand(async () => await PreviousSlideAsync(), () => CanGoPrevious());
            GoToSlideCommand = new RelayCommand<int>(async (index) => await GoToSlideAsync(index));
            TogglePlayPauseCommand = new RelayCommand(async () => await TogglePlayPauseAsync());
            ToggleAudioCommand = new RelayCommand(() => ToggleAudio());
            ShowAIAssistantCommand = new RelayCommand(() => ShowAIAssistant());
            ToggleDualPresenterModeCommand = new RelayCommand(async () => await ToggleDualPresenterModeAsync());
            StartPresentationCommand = new RelayCommand(async () => await StartPresentationAsync());
            StopPresentationCommand = new RelayCommand(async () => await StopPresentationAsync());
        }

        private void SetupEventHandlers()
        {
            if (_presentationEngine != null)
            {
                _presentationEngine.SlideChanged += OnSlideChanged;
                _presentationEngine.PresentationStateChanged += OnPresentationStateChanged;
            }

            if (_aiAssistant != null)
            {
                _aiAssistant.ResponseGenerated += OnAIResponseGenerated;
                _aiAssistant.ContentSuggested += OnContentSuggested;
            }

            if (_dualPresenterService != null)
            {
                _dualPresenterService.PresenterConnected += OnPresenterConnected;
                _dualPresenterService.PresenterDisconnected += OnPresenterDisconnected;
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                StatusMessage = "正在初始化演示引擎...";
                
                // Initialize presentation engine
                if (!_presentationEngine.IsInitialized)
                {
                    await _presentationEngine.InitializeAsync();
                }

                // Load the patriotic presentation
                StatusMessage = "正在加载演示内容...";
                await LoadPatrioticPresentationAsync();

                StatusMessage = "初始化完成";
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("初始化失败", ex));
                throw;
            }
        }

        private async Task LoadPatrioticPresentationAsync()
        {
            // Create the patriotic presentation based on the HTML content
            var presentation = CreatePatrioticPresentation();
            CurrentPresentation = presentation;
            
            // Set the presentation in the engine
            await Task.CompletedTask; // Placeholder for actual loading logic
        }

        private PresentationModel CreatePatrioticPresentation()
        {
            var presentation = new PresentationModel
            {
                Title = "家国情怀：从历史传承到时代担当",
                Description = "爱国主义精神、民族自豪感与社会责任感的当代诠释",
                Author = "教育技术解决方案",
                CurrentSlideIndex = 0
            };

            // Add slides based on the HTML content
            presentation.Slides.AddRange(CreatePatrioticSlides());

            return presentation;
        }

        private System.Collections.Generic.List<SlideModel> CreatePatrioticSlides()
        {
            var slides = new System.Collections.Generic.List<SlideModel>();

            // Slide 1: Cover
            slides.Add(new SlideModel
            {
                Id = 1,
                Title = "家国情怀：从历史传承到时代担当",
                Content = "爱国主义精神、民族自豪感与社会责任感的当代诠释",
                Theme = SlideTheme.Cover,
                BackgroundImage = "pack://application:,,,/Resources/Images/greatwall_cover.jpg",
                Duration = TimeSpan.FromSeconds(8)
            });

            // Slide 2: Table of Contents
            slides.Add(new SlideModel
            {
                Id = 2,
                Title = "目录",
                Content = "演示内容概览",
                Theme = SlideTheme.Content,
                Duration = TimeSpan.FromSeconds(6)
            });

            // Slide 3: Definition
            slides.Add(new SlideModel
            {
                Id = 3,
                Title = "一、家国情怀的内涵解析",
                Content = "个人对家庭和国家共同体的认同与热爱",
                Theme = SlideTheme.Content,
                Duration = TimeSpan.FromSeconds(10)
            });

            // Add more slides...
            // (Due to length constraints, I'll add a few key slides)

            // Slide 11: Conclusion
            slides.Add(new SlideModel
            {
                Id = 11,
                Title = "八、结语：让家国情怀照亮前行之路",
                Content = "家国情怀是中华民族的精神纽带，是穿越千年的文化基因",
                Theme = SlideTheme.Conclusion,
                BackgroundImage = "pack://application:,,,/Resources/Images/sunrise_greatwall.jpg",
                Duration = TimeSpan.FromSeconds(8)
            });

            // Slide 12: Thank You
            slides.Add(new SlideModel
            {
                Id = 12,
                Title = "感谢聆听！",
                Content = "愿家国情怀永驻心间，激励我们奋勇向前。",
                Theme = SlideTheme.Conclusion,
                BackgroundImage = "pack://application:,,,/Resources/Images/flag_doves.jpg",
                Duration = TimeSpan.FromSeconds(5)
            });

            return slides;
        }

        // Command Implementations
        private async Task NextSlideAsync()
        {
            try
            {
                await _presentationEngine.NextSlideAsync();
                UpdateCommandStates();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("切换到下一页失败", ex));
            }
        }

        private async Task PreviousSlideAsync()
        {
            try
            {
                await _presentationEngine.PreviousSlideAsync();
                UpdateCommandStates();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("切换到上一页失败", ex));
            }
        }

        private async Task GoToSlideAsync(int slideIndex)
        {
            try
            {
                await _presentationEngine.GoToSlideAsync(slideIndex);
                UpdateCommandStates();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs($"跳转到第{slideIndex + 1}页失败", ex));
            }
        }

        private async Task TogglePlayPauseAsync()
        {
            try
            {
                if (IsPlaying)
                {
                    await _presentationEngine.PausePresentationAsync();
                }
                else
                {
                    await _presentationEngine.StartPresentationAsync();
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("播放控制失败", ex));
            }
        }

        private void ToggleAudio()
        {
            try
            {
                if (IsAudioMuted)
                {
                    _presentationEngine.SetAudioVolume(0.8);
                    IsAudioMuted = false;
                }
                else
                {
                    _presentationEngine.SetAudioVolume(0.0);
                    IsAudioMuted = true;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("音频控制失败", ex));
            }
        }

        private void ShowAIAssistant()
        {
            // Implementation for showing AI assistant panel
            StatusMessage = "AI教学助手功能开发中...";
        }

        private async Task ToggleDualPresenterModeAsync()
        {
            try
            {
                if (IsDualPresenterMode)
                {
                    await _dualPresenterService.DisconnectAsync();
                    IsDualPresenterMode = false;
                    StatusMessage = "已退出双人演示模式";
                }
                else
                {
                    await _dualPresenterService.StartHostingAsync();
                    IsDualPresenterMode = true;
                    StatusMessage = "双人演示模式已启动，等待连接...";
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("双人演示模式切换失败", ex));
            }
        }

        private async Task StartPresentationAsync()
        {
            try
            {
                await _presentationEngine.StartPresentationAsync();
                IsPlaying = true;
                StatusMessage = "演示已开始";
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("开始演示失败", ex));
            }
        }

        private async Task StopPresentationAsync()
        {
            try
            {
                await _presentationEngine.StopPresentationAsync();
                IsPlaying = false;
                StatusMessage = "演示已停止";
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorEventArgs("停止演示失败", ex));
            }
        }

        // Helper Methods
        private bool CanGoNext() => _presentationEngine?.CanGoNext ?? false;
        private bool CanGoPrevious() => _presentationEngine?.CanGoPrevious ?? false;

        private void UpdateCommandStates()
        {
            OnPropertyChanged(nameof(CurrentSlide));
            OnPropertyChanged(nameof(Progress));
            OnPropertyChanged(nameof(SlideInfoText));
        }

        // Event Handlers
        private void OnSlideChanged(object sender, SlideChangedEventArgs e)
        {
            UpdateCommandStates();
            SlideChanged?.Invoke(this, e);
        }

        private void OnPresentationStateChanged(object sender, PresentationEventArgs e)
        {
            IsPlaying = e.Type == PresentationEventType.Started || e.Type == PresentationEventType.Resumed;
            PresentationStateChanged?.Invoke(this, e);
        }

        private void OnAIResponseGenerated(object sender, AIResponseEventArgs e)
        {
            StatusMessage = $"AI助手: {e.Response.Answer.Substring(0, Math.Min(50, e.Response.Answer.Length))}...";
        }

        private void OnContentSuggested(object sender, ContentSuggestionEventArgs e)
        {
            StatusMessage = $"AI建议: {e.Suggestions.Count}个内容建议";
        }

        private void OnPresenterConnected(object sender, EventArgs e)
        {
            StatusMessage = "演示者已连接";
        }

        private void OnPresenterDisconnected(object sender, EventArgs e)
        {
            StatusMessage = "演示者已断开连接";
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual void OnErrorOccurred(ErrorEventArgs e)
        {
            ErrorOccurred?.Invoke(this, e);
        }

        public void Dispose()
        {
            // Cleanup event handlers
            if (_presentationEngine != null)
            {
                _presentationEngine.SlideChanged -= OnSlideChanged;
                _presentationEngine.PresentationStateChanged -= OnPresentationStateChanged;
            }

            if (_aiAssistant != null)
            {
                _aiAssistant.ResponseGenerated -= OnAIResponseGenerated;
                _aiAssistant.ContentSuggested -= OnContentSuggested;
            }

            if (_dualPresenterService != null)
            {
                _dualPresenterService.PresenterConnected -= OnPresenterConnected;
                _dualPresenterService.PresenterDisconnected -= OnPresenterDisconnected;
            }
        }
    }
}
