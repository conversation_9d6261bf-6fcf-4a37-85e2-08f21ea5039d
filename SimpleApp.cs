using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Collections.Generic;

namespace PatrioticPresentationApp
{
    /// <summary>
    /// Simple WPF application to demonstrate the patriotic presentation functionality
    /// </summary>
    public class SimpleApp : Application
    {
        [STAThread]
        public static void Main()
        {
            var app = new SimpleApp();
            app.Run();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Create main window
            var window = new MainWindow();
            window.Show();
        }
    }

    public class MainWindow : Window
    {
        private TextBlock titleBlock;
        private TextBlock contentBlock;
        private Button nextButton;
        private Button prevButton;
        private Button loadButton;
        private ProgressBar progressBar;
        private int currentSlideIndex = 0;
        private List<SlideData> slides = new List<SlideData>();

        public MainWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void InitializeComponent()
        {
            // Window properties
            Title = "家国情怀：从历史传承到时代担当 - Patriotic Presentation App";
            Width = 1024;
            Height = 768;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new LinearGradientBrush(
                Colors.DarkRed, Colors.Maroon, new Point(0, 0), new Point(1, 1));

            // Create main grid
            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(80) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(40) });

            // Header
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20)
            };

            loadButton = new Button
            {
                Content = "加载演示 Load Presentation",
                Padding = new Thickness(15, 5, 15, 5),
                Margin = new Thickness(10),
                Background = new SolidColorBrush(Colors.Gold),
                FontWeight = FontWeights.Bold
            };
            loadButton.Click += LoadButton_Click;

            headerPanel.Children.Add(loadButton);
            Grid.SetRow(headerPanel, 0);
            mainGrid.Children.Add(headerPanel);

            // Content area
            var contentPanel = new Border
            {
                Background = new SolidColorBrush(Color.FromArgb(240, 255, 255, 255)),
                CornerRadius = new CornerRadius(10),
                Margin = new Thickness(40),
                Padding = new Thickness(30)
            };

            var contentStack = new StackPanel();

            titleBlock = new TextBlock
            {
                FontSize = 32,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Colors.DarkRed),
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20),
                TextWrapping = TextWrapping.Wrap,
                Text = "欢迎使用家国情怀演示应用"
            };

            contentBlock = new TextBlock
            {
                FontSize = 18,
                Foreground = new SolidColorBrush(Colors.Black),
                TextAlignment = TextAlignment.Left,
                Margin = new Thickness(0, 10, 0, 10),
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 28,
                Text = "这是一个展示中华民族家国情怀的互动演示应用程序。\n\n" +
                       "功能特性：\n" +
                       "• 12页爱国主义主题内容\n" +
                       "• 渐进式内容显示\n" +
                       "• 键盘导航控制\n" +
                       "• 全屏演示模式\n\n" +
                       "点击【加载演示】按钮开始，或使用键盘快捷键：\n" +
                       "• 空格键或右箭头：下一页\n" +
                       "• 左箭头：上一页\n" +
                       "• F11：全屏模式\n" +
                       "• Esc：退出全屏"
            };

            contentStack.Children.Add(titleBlock);
            contentStack.Children.Add(contentBlock);
            contentPanel.Child = contentStack;

            Grid.SetRow(contentPanel, 1);
            mainGrid.Children.Add(contentPanel);

            // Control buttons
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(20)
            };

            prevButton = new Button
            {
                Content = "◀ 上一页 Previous",
                Padding = new Thickness(20, 8, 20, 8),
                Margin = new Thickness(10),
                Background = new SolidColorBrush(Colors.LightBlue),
                IsEnabled = false
            };
            prevButton.Click += PrevButton_Click;

            nextButton = new Button
            {
                Content = "下一页 Next ▶",
                Padding = new Thickness(20, 8, 20, 8),
                Margin = new Thickness(10),
                Background = new SolidColorBrush(Colors.LightGreen),
                IsEnabled = false
            };
            nextButton.Click += NextButton_Click;

            buttonPanel.Children.Add(prevButton);
            buttonPanel.Children.Add(nextButton);

            Grid.SetRow(buttonPanel, 2);
            mainGrid.Children.Add(buttonPanel);

            // Progress bar
            progressBar = new ProgressBar
            {
                Height = 20,
                Margin = new Thickness(40, 5, 40, 5),
                Background = new SolidColorBrush(Colors.LightGray),
                Foreground = new SolidColorBrush(Colors.DarkRed),
                Value = 0
            };

            Grid.SetRow(progressBar, 3);
            mainGrid.Children.Add(progressBar);

            Content = mainGrid;

            // Keyboard handling
            KeyDown += MainWindow_KeyDown;
        }

        private void LoadSampleData()
        {
            slides = new List<SlideData>
            {
                new SlideData
                {
                    Title = "家国情怀的历史渊源",
                    Content = "从古代的忠君爱国到现代的爱国主义，家国情怀承载着中华民族的精神传承。\n\n" +
                             "• 古代文献中的家国观念\n" +
                             "• 历史人物的爱国事迹\n" +
                             "• 传统文化中的家国精神\n" +
                             "• 现代转化与发展"
                },
                new SlideData
                {
                    Title = "新时代的家国担当",
                    Content = "在新时代背景下，家国情怀体现为对国家发展的责任担当和对民族复兴的使命感。\n\n" +
                             "• 科技创新与国家实力\n" +
                             "• 文化自信与民族精神\n" +
                             "• 生态文明与可持续发展\n" +
                             "• 国际合作与人类命运共同体"
                },
                new SlideData
                {
                    Title = "爱国主义教育的重要性",
                    Content = "爱国主义教育是培养民族精神、增强国家认同的重要途径。\n\n" +
                             "• 教育目标与意义\n" +
                             "• 教育内容与方法\n" +
                             "• 教育效果与评价\n" +
                             "• 国际比较与借鉴"
                },
                new SlideData
                {
                    Title = "青年一代的使命担当",
                    Content = "青年是国家的未来，承担着实现中华民族伟大复兴的历史使命。\n\n" +
                             "• 青年的历史责任\n" +
                             "• 创新创业精神\n" +
                             "• 社会参与意识\n" +
                             "• 国际视野培养"
                },
                new SlideData
                {
                    Title = "中国梦的时代内涵",
                    Content = "中国梦是中华民族近代以来最伟大的梦想，体现了中华民族和中国人民的整体利益。\n\n" +
                             "• 国家富强梦想\n" +
                             "• 民族振兴愿景\n" +
                             "• 人民幸福追求\n" +
                             "• 世界和平贡献"
                }
            };
        }

        private void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            if (slides.Count > 0)
            {
                currentSlideIndex = 0;
                UpdateSlideDisplay();
                nextButton.IsEnabled = slides.Count > 1;
                prevButton.IsEnabled = false;
                MessageBox.Show("演示已加载！使用按钮或键盘导航。\nPresentation loaded! Use buttons or keyboard to navigate.", 
                               "加载成功 Load Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            if (currentSlideIndex < slides.Count - 1)
            {
                currentSlideIndex++;
                UpdateSlideDisplay();
            }
        }

        private void PrevButton_Click(object sender, RoutedEventArgs e)
        {
            if (currentSlideIndex > 0)
            {
                currentSlideIndex--;
                UpdateSlideDisplay();
            }
        }

        private void MainWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            switch (e.Key)
            {
                case System.Windows.Input.Key.Right:
                case System.Windows.Input.Key.Space:
                    if (nextButton.IsEnabled)
                        NextButton_Click(null, null);
                    break;
                case System.Windows.Input.Key.Left:
                    if (prevButton.IsEnabled)
                        PrevButton_Click(null, null);
                    break;
                case System.Windows.Input.Key.F11:
                    WindowState = WindowState == WindowState.Maximized ? 
                                 WindowState.Normal : WindowState.Maximized;
                    break;
                case System.Windows.Input.Key.Escape:
                    if (WindowState == WindowState.Maximized)
                        WindowState = WindowState.Normal;
                    break;
            }
        }

        private void UpdateSlideDisplay()
        {
            if (currentSlideIndex >= 0 && currentSlideIndex < slides.Count)
            {
                var slide = slides[currentSlideIndex];
                titleBlock.Text = slide.Title;
                contentBlock.Text = slide.Content;

                // Update navigation buttons
                prevButton.IsEnabled = currentSlideIndex > 0;
                nextButton.IsEnabled = currentSlideIndex < slides.Count - 1;

                // Update progress bar
                progressBar.Value = slides.Count > 1 ? 
                    (double)currentSlideIndex / (slides.Count - 1) * 100 : 100;

                // Update window title
                Title = string.Format("家国情怀演示 - 第 {0} 页，共 {1} 页", currentSlideIndex + 1, slides.Count);
            }
        }
    }

    public class SlideData
    {
        public string Title { get; set; }
        public string Content { get; set; }
    }
}
