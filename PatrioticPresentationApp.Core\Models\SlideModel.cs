using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace PatrioticPresentationApp.Core.Models
{
    /// <summary>
    /// Represents a single slide in the patriotic presentation
    /// </summary>
    public class SlideModel : INotifyPropertyChanged
    {
        private string _title;
        private string _content;
        private string _backgroundImage;
        private bool _isActive;
        private int _currentRevealIndex;

        public int Id { get; set; }
        
        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged(nameof(Title));
            }
        }

        public string Content
        {
            get => _content;
            set
            {
                _content = value;
                OnPropertyChanged(nameof(Content));
            }
        }

        public string BackgroundImage
        {
            get => _backgroundImage;
            set
            {
                _backgroundImage = value;
                OnPropertyChanged(nameof(BackgroundImage));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        public int CurrentRevealIndex
        {
            get => _currentRevealIndex;
            set
            {
                _currentRevealIndex = value;
                OnPropertyChanged(nameof(CurrentRevealIndex));
            }
        }

        public List<RevealItemModel> RevealItems { get; set; } = new List<RevealItemModel>();
        public List<AudioClipModel> AudioClips { get; set; } = new List<AudioClipModel>();
        public List<ChartDataModel> Charts { get; set; } = new List<ChartDataModel>();
        public SlideTheme Theme { get; set; } = SlideTheme.Default;
        public TimeSpan Duration { get; set; } = TimeSpan.FromSeconds(5);
        public Dictionary<string, object> CustomProperties { get; set; } = new Dictionary<string, object>();

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// Represents an item that can be revealed progressively on a slide
    /// </summary>
    public class RevealItemModel
    {
        public int Index { get; set; }
        public string Content { get; set; }
        public RevealItemType Type { get; set; }
        public TimeSpan Delay { get; set; }
        public AnimationType Animation { get; set; }
        public string IconClass { get; set; }
        public string HighlightText { get; set; }
        public bool IsVisible { get; set; }
    }

    /// <summary>
    /// Represents an audio clip associated with a slide
    /// </summary>
    public class AudioClipModel
    {
        public string Id { get; set; }
        public string FilePath { get; set; }
        public string Description { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool AutoPlay { get; set; }
        public double Volume { get; set; } = 1.0;
    }

    /// <summary>
    /// Represents chart data for visualization
    /// </summary>
    public class ChartDataModel
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public ChartType Type { get; set; }
        public List<ChartSeriesModel> Series { get; set; } = new List<ChartSeriesModel>();
        public List<string> Labels { get; set; } = new List<string>();
        public Dictionary<string, object> Options { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Represents a data series in a chart
    /// </summary>
    public class ChartSeriesModel
    {
        public string Name { get; set; }
        public List<double> Data { get; set; } = new List<double>();
        public string Color { get; set; }
        public ChartSeriesType Type { get; set; }
    }

    /// <summary>
    /// Enumeration of reveal item types
    /// </summary>
    public enum RevealItemType
    {
        Text,
        BulletPoint,
        Quote,
        Highlight,
        Image,
        Chart
    }

    /// <summary>
    /// Enumeration of animation types
    /// </summary>
    public enum AnimationType
    {
        FadeIn,
        SlideUp,
        ScaleIn,
        PulseHighlight,
        UnderlinePulse
    }

    /// <summary>
    /// Enumeration of slide themes
    /// </summary>
    public enum SlideTheme
    {
        Default,
        Cover,
        Content,
        Quote,
        Chart,
        Conclusion
    }

    /// <summary>
    /// Enumeration of chart types
    /// </summary>
    public enum ChartType
    {
        Bar,
        Line,
        Pie,
        Area,
        Column
    }

    /// <summary>
    /// Enumeration of chart series types
    /// </summary>
    public enum ChartSeriesType
    {
        Column,
        Line,
        Area,
        Spline
    }
}
