using System;
using System.Collections.Generic;

namespace PatrioticPresentationApp.Core.Models
{
    /// <summary>
    /// Audience member information
    /// </summary>
    public class AudienceMember
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; }
        public string Email { get; set; }
        public DateTime JoinTime { get; set; } = DateTime.Now;
        public DateTime? LeaveTime { get; set; }
        public AudienceStatus Status { get; set; } = AudienceStatus.Connected;
        public string DeviceInfo { get; set; }
        public string IPAddress { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
        public List<string> Roles { get; set; } = new List<string>();
        public AudienceProfile Profile { get; set; }
    }

    /// <summary>
    /// Audience registration data
    /// </summary>
    public class AudienceRegistration
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string Organization { get; set; }
        public string Role { get; set; }
        public string DeviceInfo { get; set; }
        public Dictionary<string, string> CustomFields { get; set; } = new Dictionary<string, string>();
        public bool AcceptTerms { get; set; }
        public DateTime RegistrationTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Poll definition
    /// </summary>
    public class PollDefinition
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Question { get; set; }
        public PollType Type { get; set; }
        public List<string> Options { get; set; } = new List<string>();
        public bool AllowMultipleChoice { get; set; } = false;
        public bool IsAnonymous { get; set; } = true;
        public TimeSpan? Duration { get; set; }
        public int? TargetSlide { get; set; }
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; }
    }

    /// <summary>
    /// Active poll instance
    /// </summary>
    public class ActivePoll
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public PollDefinition Definition { get; set; }
        public PollStatus Status { get; set; } = PollStatus.Created;
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<PollResponse> Responses { get; set; } = new List<PollResponse>();
        public int TargetAudienceCount { get; set; }
        public Dictionary<string, object> Settings { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Poll response from audience
    /// </summary>
    public class PollResponse
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PollId { get; set; }
        public string AudienceId { get; set; }
        public string SelectedOption { get; set; }
        public List<string> SelectedOptions { get; set; } = new List<string>();
        public string TextResponse { get; set; }
        public DateTime SubmittedTime { get; set; } = DateTime.Now;
        public TimeSpan ResponseTime { get; set; }
        public bool IsValid { get; set; } = true;
    }

    /// <summary>
    /// Poll results summary
    /// </summary>
    public class PollResults
    {
        public string PollId { get; set; }
        public string Question { get; set; }
        public int TotalResponses { get; set; }
        public Dictionary<string, int> ResponseCounts { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, double> ResponsePercentages { get; set; } = new Dictionary<string, double>();
        public TimeSpan AverageResponseTime { get; set; }
        public DateTime GeneratedTime { get; set; } = DateTime.Now;
        public List<string> TopResponses { get; set; } = new List<string>();
    }

    /// <summary>
    /// Audience question
    /// </summary>
    public class AudienceQuestion
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string AudienceId { get; set; }
        public string Question { get; set; }
        public int SlideIndex { get; set; }
        public DateTime SubmittedTime { get; set; } = DateTime.Now;
        public QuestionStatus Status { get; set; } = QuestionStatus.Pending;
        public int Votes { get; set; } = 0;
        public string Answer { get; set; }
        public DateTime? ApprovedTime { get; set; }
        public DateTime? AnsweredTime { get; set; }
        public string RejectionReason { get; set; }
        public List<string> Tags { get; set; } = new List<string>();
        public QuestionPriority Priority { get; set; } = QuestionPriority.Normal;
    }

    /// <summary>
    /// Feedback submission
    /// </summary>
    public class FeedbackSubmission
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string AudienceId { get; set; }
        public int SlideIndex { get; set; }
        public FeedbackType Type { get; set; }
        public int Rating { get; set; }
        public string Comment { get; set; }
        public List<string> Categories { get; set; } = new List<string>();
        public DateTime SubmittedTime { get; set; } = DateTime.Now;
        public bool IsAnonymous { get; set; } = true;
        public Dictionary<string, object> CustomData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Feedback request to audience
    /// </summary>
    public class FeedbackRequest
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; }
        public string Description { get; set; }
        public FeedbackType Type { get; set; }
        public List<string> Questions { get; set; } = new List<string>();
        public int? TargetSlide { get; set; }
        public TimeSpan? Duration { get; set; }
        public bool IsRequired { get; set; } = false;
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Feedback summary
    /// </summary>
    public class FeedbackSummary
    {
        public string PresentationId { get; set; }
        public int TotalFeedback { get; set; }
        public double AverageRating { get; set; }
        public Dictionary<int, double> SlideRatings { get; set; } = new Dictionary<int, double>();
        public Dictionary<FeedbackType, int> FeedbackTypeCounts { get; set; } = new Dictionary<FeedbackType, int>();
        public List<string> TopComments { get; set; } = new List<string>();
        public List<string> ImprovementSuggestions { get; set; } = new List<string>();
        public DateTime GeneratedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Reaction summary
    /// </summary>
    public class ReactionSummary
    {
        public int SlideIndex { get; set; }
        public Dictionary<ReactionType, int> ReactionCounts { get; set; } = new Dictionary<ReactionType, int>();
        public int TotalReactions { get; set; }
        public ReactionType MostPopularReaction { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public List<ReactionTrend> Trends { get; set; } = new List<ReactionTrend>();
    }

    /// <summary>
    /// Reaction trend over time
    /// </summary>
    public class ReactionTrend
    {
        public DateTime Timestamp { get; set; }
        public ReactionType Type { get; set; }
        public int Count { get; set; }
        public double Intensity { get; set; }
    }

    /// <summary>
    /// Engagement metrics
    /// </summary>
    public class EngagementMetrics
    {
        public string SessionId { get; set; }
        public double OverallEngagementScore { get; set; }
        public int ActiveParticipants { get; set; }
        public int TotalInteractions { get; set; }
        public TimeSpan AverageSessionDuration { get; set; }
        public Dictionary<string, double> MetricsByType { get; set; } = new Dictionary<string, double>();
        public List<EngagementPeriod> HighEngagementPeriods { get; set; } = new List<EngagementPeriod>();
        public DateTime CalculatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Audience insights
    /// </summary>
    public class AudienceInsights
    {
        public string SessionId { get; set; }
        public int TotalAudience { get; set; }
        public Dictionary<string, int> DemographicBreakdown { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, double> BehaviorPatterns { get; set; } = new Dictionary<string, double>();
        public List<AudienceSegmentInsight> Segments { get; set; } = new List<AudienceSegmentInsight>();
        public List<string> KeyFindings { get; set; } = new List<string>();
        public List<string> Recommendations { get; set; } = new List<string>();
        public DateTime GeneratedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Participation report
    /// </summary>
    public class ParticipationReport
    {
        public string SessionId { get; set; }
        public int TotalParticipants { get; set; }
        public double ParticipationRate { get; set; }
        public Dictionary<string, int> ActivityCounts { get; set; } = new Dictionary<string, int>();
        public List<ParticipantSummary> TopParticipants { get; set; } = new List<ParticipantSummary>();
        public Dictionary<int, double> SlideParticipationRates { get; set; } = new Dictionary<int, double>();
        public DateTime GeneratedTime { get; set; } = DateTime.Now;
    }

    // Supporting classes
    public class EngagementPeriod
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public double EngagementScore { get; set; }
        public string Context { get; set; }
        public List<string> Triggers { get; set; } = new List<string>();
    }

    public class AudienceSegmentInsight
    {
        public string SegmentName { get; set; }
        public int Count { get; set; }
        public double EngagementScore { get; set; }
        public List<string> Characteristics { get; set; } = new List<string>();
        public List<string> PreferredContent { get; set; } = new List<string>();
    }

    public class ParticipantSummary
    {
        public string AudienceId { get; set; }
        public string Name { get; set; }
        public int InteractionCount { get; set; }
        public double EngagementScore { get; set; }
        public TimeSpan ActiveTime { get; set; }
        public List<string> Activities { get; set; } = new List<string>();
    }

    // Enumerations
    public enum AudienceStatus
    {
        Connected,
        Disconnected,
        Inactive,
        Blocked,
        Moderator
    }

    public enum PollType
    {
        MultipleChoice,
        SingleChoice,
        TrueFalse,
        Rating,
        OpenText,
        Ranking
    }

    public enum PollStatus
    {
        Created,
        Active,
        Paused,
        Closed,
        Cancelled
    }

    public enum QuestionStatus
    {
        Pending,
        Approved,
        Rejected,
        Answered,
        Archived
    }

    public enum QuestionPriority
    {
        Low,
        Normal,
        High,
        Urgent
    }

    public enum ReactionType
    {
        Like,
        Love,
        Laugh,
        Wow,
        Sad,
        Angry,
        Confused,
        Applause,
        ThumbsUp,
        ThumbsDown
    }

    public enum NotificationType
    {
        Info,
        Warning,
        Success,
        Error,
        Poll,
        Question,
        Feedback,
        General
    }

    // Event Arguments
    public class AudienceJoinedEventArgs : EventArgs
    {
        public AudienceMember Member { get; set; }
        public DateTime JoinTime { get; set; }
    }

    public class AudienceLeftEventArgs : EventArgs
    {
        public AudienceMember Member { get; set; }
        public DateTime LeaveTime { get; set; }
        public string Reason { get; set; }
    }

    public class PollResponseEventArgs : EventArgs
    {
        public string PollId { get; set; }
        public PollResponse Response { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class QuestionSubmittedEventArgs : EventArgs
    {
        public AudienceQuestion Question { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class FeedbackReceivedEventArgs : EventArgs
    {
        public FeedbackSubmission Feedback { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class ReactionEventArgs : EventArgs
    {
        public string AudienceId { get; set; }
        public ReactionType Reaction { get; set; }
        public int SlideIndex { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
