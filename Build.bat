@echo off
echo ===================================
echo 家国情怀演示应用构建脚本
echo Patriotic Presentation App Builder
echo ===================================
echo.

set MSBUILD="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe"
set SOLUTION=PatrioticPresentationApp.sln
set OUTPUT_DIR=Deploy\PatrioticPresentationApp

echo 检查构建工具 Checking build tools...
if not exist %MSBUILD% (
    echo 错误：未找到 MSBuild
    echo Error: MSBuild not found
    pause
    exit /b 1
)

echo 清理输出目录 Cleaning output directory...
if exist Deploy rmdir /s /q Deploy
mkdir Deploy
mkdir %OUTPUT_DIR%

echo 构建解决方案 Building solution...
%MSBUILD% %SOLUTION% /p:Configuration=Release /p:Platform="Any CPU" /p:OutputPath=..\..\%OUTPUT_DIR%\ /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo 构建失败 Build failed
    pause
    exit /b 1
)

echo 复制配置文件 Copying configuration files...
copy PatrioticPresentationApp\App.config "%OUTPUT_DIR%\PatrioticPresentationApp.exe.config"

echo 创建资源目录 Creating resource directories...
mkdir "%OUTPUT_DIR%\Resources"
mkdir "%OUTPUT_DIR%\Resources\Audio"
mkdir "%OUTPUT_DIR%\Resources\Images"
mkdir "%OUTPUT_DIR%\Resources\Slides"
mkdir "%OUTPUT_DIR%\Resources\Styles"

echo 创建示例演示文件 Creating sample presentation...
echo { > "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Id": "sample-patriotic-presentation", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Title": "家国情怀：从历史传承到时代担当", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Description": "爱国主义教育互动演示", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Author": "Educational Technology Solutions", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Version": "1.0.0", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Language": "zh-CN", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Theme": "Patriotic", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "CurrentSlideIndex": 0, >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Slides": [ >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo     { >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "Id": "slide-001", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "Index": 0, >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "Title": "家国情怀的历史渊源", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "Content": "从古代的忠君爱国到现代的爱国主义，家国情怀承载着中华民族的精神传承。", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "Theme": "PatrioticRed", >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "RevealItems": [], >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "AudioClips": [], >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo       "Charts": [] >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo     } >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   ], >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   "Settings": { >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo     "AutoPlay": false, >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo     "ShowProgressBar": true, >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo     "EnableKeyboardNavigation": true >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo   } >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"
echo } >> "%OUTPUT_DIR%\Resources\SamplePresentation.json"

echo 创建启动脚本 Creating startup script...
echo @echo off > "%OUTPUT_DIR%\启动应用.bat"
echo echo 启动家国情怀演示应用... >> "%OUTPUT_DIR%\启动应用.bat"
echo echo Starting Patriotic Presentation App... >> "%OUTPUT_DIR%\启动应用.bat"
echo start PatrioticPresentationApp.exe >> "%OUTPUT_DIR%\启动应用.bat"

echo 创建用户指南 Creating user guide...
echo # 家国情怀演示应用用户指南 > "Deploy\用户指南.txt"
echo # Patriotic Presentation App User Guide >> "Deploy\用户指南.txt"
echo. >> "Deploy\用户指南.txt"
echo ## 快速开始 Quick Start >> "Deploy\用户指南.txt"
echo 1. 双击"启动应用.bat"运行程序 >> "Deploy\用户指南.txt"
echo 2. 点击"加载演示"按钮 >> "Deploy\用户指南.txt"
echo 3. 选择 Resources\SamplePresentation.json >> "Deploy\用户指南.txt"
echo 4. 使用键盘控制演示： >> "Deploy\用户指南.txt"
echo    - 空格键或右箭头：下一页 >> "Deploy\用户指南.txt"
echo    - 左箭头：上一页 >> "Deploy\用户指南.txt"
echo    - F11：全屏模式 >> "Deploy\用户指南.txt"
echo    - Esc：退出全屏 >> "Deploy\用户指南.txt"
echo. >> "Deploy\用户指南.txt"
echo ## 功能特性 Features >> "Deploy\用户指南.txt"
echo - 爱国主义主题内容 >> "Deploy\用户指南.txt"
echo - 渐进式内容显示 >> "Deploy\用户指南.txt"
echo - 键盘导航控制 >> "Deploy\用户指南.txt"
echo - 全屏演示模式 >> "Deploy\用户指南.txt"

echo.
echo ===================================
echo 构建完成！Build completed!
echo ===================================
echo 输出目录 Output directory: %OUTPUT_DIR%
echo.
echo 下一步 Next steps:
echo 1. 进入 Deploy 目录
echo 2. 运行 PatrioticPresentationApp\启动应用.bat
echo 3. 查看用户指南.txt了解使用方法
echo.

set /p answer=是否现在测试应用程序？Test the application now? (y/n): 
if /i "%answer%"=="y" (
    cd %OUTPUT_DIR%
    start 启动应用.bat
)

echo.
echo 构建脚本执行完成 Build script completed
pause
