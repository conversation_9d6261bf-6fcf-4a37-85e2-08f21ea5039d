<Window x:Class="PatrioticPresentationApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="家国情怀：从历史传承到时代担当 - 交互式演示" 
        Height="720" Width="1280"
        MinHeight="600" MinWidth="800"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource DarkBackgroundBrush}"
        FontFamily="{StaticResource ChineseFontFamily}">
    
    <Window.Resources>
        <!-- Slide Container Style -->
        <Style x:Key="SlideContainerStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource LightBackgroundBrush}"/>
            <Setter Property="CornerRadius" Value="0"/>
            <Setter Property="ClipToBounds" Value="True"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Control Panel Style -->
        <Style x:Key="ControlPanelStyle" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <SolidColorBrush Color="Black" Opacity="0.85"/>
                </Setter.Value>
            </Setter>
            <Setter Property="CornerRadius" Value="25"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" Opacity="0.4" BlurRadius="15"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Slide Transition Storyboards -->
        <Storyboard x:Key="SlideInFromRight">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="1280" To="0" Duration="0:0:0.8">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.8"/>
        </Storyboard>

        <Storyboard x:Key="SlideOutToLeft">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="0" To="-1280" Duration="0:0:0.8">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.8"/>
        </Storyboard>

        <Storyboard x:Key="FadeIn">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.8"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="0.95" To="1" Duration="0:0:0.8"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="0.95" To="1" Duration="0:0:0.8"/>
        </Storyboard>
    </Window.Resources>

    <Grid>
        <!-- Main Content Area -->
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Slide Display Area -->
        <Border Grid.Row="0" Style="{StaticResource SlideContainerStyle}">
            <!-- Slide Content Container -->
            <Grid x:Name="SlideContainer" ClipToBounds="True">
                <!-- Background Image -->
                <Image x:Name="BackgroundImage" 
                       Stretch="UniformToFill" 
                       Opacity="0.4"
                       Source="{Binding CurrentSlide.BackgroundImage}"/>

                <!-- Gradient Overlay -->
                <Rectangle x:Name="GradientOverlay" Opacity="0.6">
                    <Rectangle.Fill>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                            <GradientStop Color="#00000000" Offset="0"/>
                            <GradientStop Color="#33000000" Offset="0.5"/>
                            <GradientStop Color="#66000000" Offset="1"/>
                        </LinearGradientBrush>
                    </Rectangle.Fill>
                </Rectangle>

                <!-- Slide Content -->
                <ContentPresenter x:Name="SlideContentPresenter" 
                                Content="{Binding CurrentSlide}"
                                Margin="80,60,80,120">
                    <ContentPresenter.RenderTransform>
                        <TransformGroup>
                            <ScaleTransform/>
                            <TranslateTransform/>
                        </TransformGroup>
                    </ContentPresenter.RenderTransform>
                </ContentPresenter>

                <!-- Loading Indicator -->
                <Grid x:Name="LoadingIndicator" 
                      Background="#80000000" 
                      Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center" 
                              VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" 
                                   Width="200" Height="4"
                                   Style="{StaticResource PresentationProgressStyle}"/>
                        <TextBlock Text="正在加载演示内容..." 
                                 Foreground="White" 
                                 FontSize="16" 
                                 Margin="0,20,0,0"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- Control Panel -->
        <Border Grid.Row="1" 
                Style="{StaticResource ControlPanelStyle}"
                HorizontalAlignment="Center"
                VerticalAlignment="Bottom"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" 
                       VerticalAlignment="Center">
                
                <!-- Previous Button -->
                <Button x:Name="PreviousButton"
                       Style="{StaticResource PatrioticButtonStyle}"
                       Width="44" Height="44"
                       Margin="0,0,10,0"
                       Click="PreviousButton_Click"
                       ToolTip="上一页 (←)">
                    <Path Data="M15,18L9,12L15,6V18Z" 
                          Fill="White" 
                          Width="12" Height="12"/>
                </Button>

                <!-- Play/Pause Button -->
                <Button x:Name="PlayPauseButton"
                       Style="{StaticResource PatrioticButtonStyle}"
                       Width="44" Height="44"
                       Margin="0,0,10,0"
                       Click="PlayPauseButton_Click"
                       ToolTip="播放/暂停 (P)">
                    <Path x:Name="PlayPauseIcon"
                          Data="M8,5V19L19,12M6,4V20H8V4H6M10,4V20H12V4H10Z" 
                          Fill="White" 
                          Width="12" Height="12"/>
                </Button>

                <!-- Next Button -->
                <Button x:Name="NextButton"
                       Style="{StaticResource PatrioticButtonStyle}"
                       Width="44" Height="44"
                       Margin="0,0,20,0"
                       Click="NextButton_Click"
                       ToolTip="下一页 (→)">
                    <Path Data="M9,18L15,12L9,6V18Z" 
                          Fill="White" 
                          Width="12" Height="12"/>
                </Button>

                <!-- Audio Control Button -->
                <Button x:Name="AudioButton"
                       Width="44" Height="44"
                       Margin="0,0,20,0"
                       Click="AudioButton_Click"
                       ToolTip="音频控制 (M)"
                       Background="{StaticResource PrimaryRedBrush}"
                       BorderThickness="0"
                       Style="{StaticResource PatrioticButtonStyle}">
                    <Path x:Name="AudioIcon"
                          Data="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18.01,19.86 21,16.28 21,12C21,7.72 18.01,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z" 
                          Fill="White" 
                          Width="12" Height="12"/>
                </Button>

                <!-- Progress Bar -->
                <ProgressBar x:Name="PresentationProgress"
                           Width="300" Height="6"
                           Style="{StaticResource PresentationProgressStyle}"
                           Value="{Binding Progress}"
                           Margin="0,0,20,0"
                           Cursor="Hand"
                           MouseLeftButtonDown="PresentationProgress_MouseLeftButtonDown"/>

                <!-- Slide Info -->
                <TextBlock x:Name="SlideInfo"
                         Text="{Binding SlideInfoText}"
                         Foreground="White"
                         FontSize="14"
                         VerticalAlignment="Center"
                         Margin="0,0,20,0"/>

                <!-- AI Assistant Button -->
                <Button x:Name="AIAssistantButton"
                       Style="{StaticResource PatrioticButtonStyle}"
                       Width="44" Height="44"
                       Margin="0,0,10,0"
                       Click="AIAssistantButton_Click"
                       ToolTip="AI教学助手"
                       Background="{StaticResource PrimaryGreenBrush}">
                    <Path Data="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7A1,1 0 0,1 12,8H11V10A1,1 0 0,1 10,11H9V12H16A2,2 0 0,1 18,14V16A2,2 0 0,1 16,18H15V20A2,2 0 0,1 13,22H11A2,2 0 0,1 9,20V18H8A2,2 0 0,1 6,16V14A2,2 0 0,1 8,12H9V11A1,1 0 0,1 10,10V8A1,1 0 0,1 11,7V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2Z" 
                          Fill="White" 
                          Width="12" Height="12"/>
                </Button>

                <!-- Dual Presenter Button -->
                <Button x:Name="DualPresenterButton"
                       Style="{StaticResource PatrioticButtonStyle}"
                       Width="44" Height="44"
                       Margin="0,0,10,0"
                       Click="DualPresenterButton_Click"
                       ToolTip="双人演示模式"
                       Background="{StaticResource GoldAccentBrush}">
                    <Path Data="M16,4C18.21,4 20,5.79 20,8C20,10.21 18.21,12 16,12C13.79,12 12,10.21 12,8C12,5.79 13.79,4 16,4M16,14C18.67,14 22,15.33 22,18V20H10V18C10,15.33 13.33,14 16,14M8,4C10.21,4 12,5.79 12,8C12,10.21 10.21,12 8,12C5.79,12 4,10.21 4,8C4,5.79 5.79,4 8,4M8,14C10.67,14 14,15.33 14,18V20H2V18C2,15.33 5.33,14 8,14Z" 
                          Fill="White" 
                          Width="12" Height="12"/>
                </Button>

                <!-- Fullscreen Button -->
                <Button x:Name="FullscreenButton"
                       Style="{StaticResource PatrioticButtonStyle}"
                       Width="44" Height="44"
                       Click="FullscreenButton_Click"
                       ToolTip="全屏模式 (F11)">
                    <Path Data="M5,5H10V7H7V10H5V5M14,5H19V10H17V7H14V5M17,14H19V19H14V17H17V14M10,17V19H5V14H7V17H10Z" 
                          Fill="White" 
                          Width="12" Height="12"/>
                </Button>
            </StackPanel>
        </Border>

        <!-- Keyboard Shortcuts Help -->
        <Border x:Name="HelpOverlay" 
                Grid.RowSpan="2"
                Background="#CC000000" 
                Visibility="Collapsed">
            <Border Background="White" 
                    CornerRadius="10" 
                    Padding="30"
                    MaxWidth="500"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">
                <StackPanel>
                    <TextBlock Text="键盘快捷键" 
                             FontSize="24" 
                             FontWeight="Bold" 
                             Margin="0,0,0,20"
                             HorizontalAlignment="Center"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="← →" FontWeight="Bold"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="上一页/下一页"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="空格" FontWeight="Bold"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="下一页/显示下一项"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="P" FontWeight="Bold"/>
                        <TextBlock Grid.Row="2" Grid.Column="2" Text="播放/暂停"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="M" FontWeight="Bold"/>
                        <TextBlock Grid.Row="3" Grid.Column="2" Text="静音/取消静音"/>
                        
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="F11" FontWeight="Bold"/>
                        <TextBlock Grid.Row="4" Grid.Column="2" Text="全屏模式"/>
                        
                        <TextBlock Grid.Row="5" Grid.Column="0" Text="Esc" FontWeight="Bold"/>
                        <TextBlock Grid.Row="5" Grid.Column="2" Text="退出全屏/关闭帮助"/>
                    </Grid>
                    <Button Content="关闭" 
                          Style="{StaticResource PatrioticButtonStyle}"
                          HorizontalAlignment="Center"
                          Margin="0,20,0,0"
                          Click="CloseHelp_Click"/>
                </StackPanel>
            </Border>
        </Border>
    </Grid>
</Window>
