<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>
    
    <appSettings>
        <!-- AI Service Configuration -->
        <add key="AIServiceEndpoint" value="" />
        <add key="AIServiceKey" value="" />
        
        <!-- Presentation Settings -->
        <add key="DefaultSlideTransitionDuration" value="800" />
        <add key="AutoPlayInterval" value="5000" />
        <add key="EnableAudioPlayback" value="true" />
        
        <!-- Dual Presenter Configuration -->
        <add key="EnableDualPresenterMode" value="true" />
        <add key="NetworkPort" value="8080" />
        
        <!-- Audience Interaction Settings -->
        <add key="EnableAudiencePolling" value="true" />
        <add key="MaxAudienceConnections" value="100" />
        
        <!-- Analytics and Export -->
        <add key="EnableAnalytics" value="true" />
        <add key="ExportPath" value="./Exports/" />
        
        <!-- Accessibility Features -->
        <add key="EnableHighContrast" value="false" />
        <add key="EnableScreenReader" value="false" />
        <add key="FontSizeMultiplier" value="1.0" />
    </appSettings>
    
    <connectionStrings>
        <!-- Local database for offline functionality -->
        <add name="LocalDatabase" connectionString="Data Source=|DataDirectory|\PresentationData.db;Version=3;" providerName="System.Data.SQLite" />
    </connectionStrings>
</configuration>
