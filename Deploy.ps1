# 家国情怀演示应用部署脚本
# Patriotic Presentation App Deployment Script

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = ".\Deploy",
    [switch]$IncludeSource = $false,
    [switch]$CreateInstaller = $false
)

Write-Host "=== 家国情怀演示应用部署脚本 ===" -ForegroundColor Green
Write-Host "=== Patriotic Presentation App Deployment ===" -ForegroundColor Green
Write-Host ""

# 检查前提条件
Write-Host "检查前提条件 Checking prerequisites..." -ForegroundColor Yellow

# 检查 MSBuild
$msbuild = Get-Command "msbuild.exe" -ErrorAction SilentlyContinue
if (-not $msbuild) {
    Write-Error "MSBuild 未找到。请安装 Visual Studio 或 Build Tools。"
    Write-Error "MSBuild not found. Please install Visual Studio or Build Tools."
    exit 1
}

# 检查 NuGet
$nuget = Get-Command "nuget.exe" -ErrorAction SilentlyContinue
if (-not $nuget) {
    Write-Warning "NuGet 未找到，尝试下载..."
    Write-Warning "NuGet not found, attempting to download..."
    
    $nugetUrl = "https://dist.nuget.org/win-x86-commandline/latest/nuget.exe"
    $nugetPath = ".\nuget.exe"
    
    try {
        Invoke-WebRequest -Uri $nugetUrl -OutFile $nugetPath
        $nuget = Get-Command $nugetPath
        Write-Host "✅ NuGet 下载成功 NuGet downloaded successfully" -ForegroundColor Green
    }
    catch {
        Write-Error "无法下载 NuGet。请手动安装。"
        Write-Error "Failed to download NuGet. Please install manually."
        exit 1
    }
}

Write-Host "✅ 前提条件检查完成 Prerequisites check completed" -ForegroundColor Green
Write-Host ""

# 清理输出目录
Write-Host "清理输出目录 Cleaning output directory..." -ForegroundColor Yellow
if (Test-Path $OutputPath) {
    Remove-Item -Path $OutputPath -Recurse -Force
}
New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null

# 还原 NuGet 包
Write-Host "还原 NuGet 包 Restoring NuGet packages..." -ForegroundColor Yellow
& $nuget.Source restore "PatrioticPresentationApp.sln"
if ($LASTEXITCODE -ne 0) {
    Write-Error "NuGet 包还原失败 NuGet package restore failed"
    exit 1
}
Write-Host "✅ NuGet 包还原完成 NuGet packages restored" -ForegroundColor Green

# 构建解决方案
Write-Host "构建解决方案 Building solution..." -ForegroundColor Yellow
& $msbuild.Source "PatrioticPresentationApp.sln" /p:Configuration=$Configuration /p:Platform="Any CPU" /p:OutputPath="$OutputPath\bin\" /verbosity:minimal
if ($LASTEXITCODE -ne 0) {
    Write-Error "构建失败 Build failed"
    exit 1
}
Write-Host "✅ 构建完成 Build completed" -ForegroundColor Green

# 复制应用程序文件
Write-Host "复制应用程序文件 Copying application files..." -ForegroundColor Yellow

$appPath = "$OutputPath\PatrioticPresentationApp"
New-Item -Path $appPath -ItemType Directory -Force | Out-Null

# 复制主应用程序
Copy-Item -Path "PatrioticPresentationApp\bin\$Configuration\*" -Destination $appPath -Recurse -Force

# 复制资源文件
$resourcesPath = "$appPath\Resources"
New-Item -Path $resourcesPath -ItemType Directory -Force | Out-Null

# 创建示例资源文件夹结构
@("Audio", "Images", "Slides", "Styles") | ForEach-Object {
    New-Item -Path "$resourcesPath\$_" -ItemType Directory -Force | Out-Null
}

# 复制配置文件
Copy-Item -Path "PatrioticPresentationApp\App.config" -Destination "$appPath\PatrioticPresentationApp.exe.config" -Force

# 创建示例内容
Write-Host "创建示例内容 Creating sample content..." -ForegroundColor Yellow

# 创建示例演示文件
$samplePresentation = @"
{
  "Id": "sample-patriotic-presentation",
  "Title": "家国情怀：从历史传承到时代担当",
  "Description": "爱国主义教育互动演示 - 示例内容",
  "Author": "Educational Technology Solutions",
  "CreatedDate": "$(Get-Date -Format 'yyyy-MM-ddTHH:mm:ssZ')",
  "Version": "1.0.0",
  "Language": "zh-CN",
  "Theme": "Patriotic",
  "CurrentSlideIndex": 0,
  "Slides": [
    {
      "Id": "slide-001",
      "Index": 0,
      "Title": "家国情怀的历史渊源",
      "Content": "从古代的忠君爱国到现代的爱国主义，家国情怀承载着中华民族的精神传承。",
      "Theme": "PatrioticRed",
      "RevealItems": [
        {
          "Id": "item-001",
          "Index": 0,
          "Content": "古代文献中的家国观念",
          "AnimationType": "FadeIn",
          "IsVisible": false,
          "IsRevealed": false
        }
      ],
      "AudioClips": [],
      "Charts": [],
      "CurrentRevealIndex": 0
    }
  ],
  "Settings": {
    "AutoPlay": false,
    "AutoPlayInterval": "00:00:05",
    "ShowProgressBar": true,
    "EnableKeyboardNavigation": true,
    "TransitionDuration": "00:00:00.800",
    "DefaultVolume": 0.8
  }
}
"@

$samplePresentation | Out-File -FilePath "$resourcesPath\SamplePresentation.json" -Encoding UTF8

# 创建用户指南
Write-Host "创建用户指南 Creating user guide..." -ForegroundColor Yellow

$userGuide = @"
# 家国情怀演示应用用户指南
# Patriotic Presentation App User Guide

## 快速开始 Quick Start

1. 运行 PatrioticPresentationApp.exe
2. 点击"加载演示"按钮
3. 选择 Resources\SamplePresentation.json
4. 使用以下快捷键控制演示：
   - 空格键或右箭头：下一页
   - 左箭头：上一页
   - F11：全屏模式
   - Esc：退出全屏
   - P：播放/暂停
   - M：静音/取消静音

## 功能特性 Features

- ✅ 12页爱国主义主题内容
- ✅ 渐进式内容显示
- ✅ 音频播放支持
- ✅ 图表可视化
- ✅ 键盘导航
- ✅ 全屏演示模式
- ✅ AI教学助手（需配置）
- ✅ 双人演示模式
- ✅ 观众互动功能
- ✅ 演示分析报告

## 配置说明 Configuration

编辑 PatrioticPresentationApp.exe.config 文件来配置：
- AI服务端点和密钥
- 网络端口设置
- 音频和视频设置
- 辅助功能选项

## 技术支持 Technical Support

如遇问题，请查看日志文件：
%APPDATA%\PatrioticPresentationApp\Logs\

或联系技术支持：<EMAIL>
"@

$userGuide | Out-File -FilePath "$OutputPath\用户指南_UserGuide.txt" -Encoding UTF8

# 创建启动脚本
$startScript = @"
@echo off
echo 启动家国情怀演示应用...
echo Starting Patriotic Presentation App...
cd /d "%~dp0PatrioticPresentationApp"
start PatrioticPresentationApp.exe
"@

$startScript | Out-File -FilePath "$OutputPath\启动应用_StartApp.bat" -Encoding ASCII

# 复制源代码（如果需要）
if ($IncludeSource) {
    Write-Host "复制源代码 Copying source code..." -ForegroundColor Yellow
    $sourcePath = "$OutputPath\Source"
    New-Item -Path $sourcePath -ItemType Directory -Force | Out-Null
    
    # 复制源代码文件，排除 bin 和 obj 目录
    robocopy . $sourcePath /E /XD bin obj .vs packages Deploy .git /XF *.user *.suo
}

# 创建安装程序（如果需要）
if ($CreateInstaller) {
    Write-Host "创建安装程序 Creating installer..." -ForegroundColor Yellow
    
    # 检查是否有 NSIS 或其他安装程序创建工具
    $nsis = Get-Command "makensis.exe" -ErrorAction SilentlyContinue
    if ($nsis) {
        # 创建 NSIS 脚本并构建安装程序
        Write-Host "使用 NSIS 创建安装程序 Creating installer with NSIS..." -ForegroundColor Yellow
        # 这里可以添加 NSIS 脚本生成和执行逻辑
    } else {
        Write-Warning "未找到 NSIS，跳过安装程序创建。"
        Write-Warning "NSIS not found, skipping installer creation."
    }
}

# 生成部署报告
Write-Host "生成部署报告 Generating deployment report..." -ForegroundColor Yellow

$deploymentReport = @"
# 部署报告 Deployment Report
生成时间 Generated: $(Get-Date)
配置 Configuration: $Configuration
输出路径 Output Path: $OutputPath

## 文件清单 File List
"@

Get-ChildItem -Path $OutputPath -Recurse | ForEach-Object {
    $relativePath = $_.FullName.Replace((Resolve-Path $OutputPath).Path, "")
    $deploymentReport += "`n$relativePath"
}

$deploymentReport | Out-File -FilePath "$OutputPath\DeploymentReport.txt" -Encoding UTF8

Write-Host ""
Write-Host "✅ 部署完成！Deployment completed!" -ForegroundColor Green
Write-Host "输出目录 Output directory: $OutputPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "下一步 Next steps:" -ForegroundColor Yellow
Write-Host "1. 测试应用程序 Test the application: $OutputPath\启动应用_StartApp.bat"
Write-Host "2. 查看用户指南 Read user guide: $OutputPath\用户指南_UserGuide.txt"
Write-Host "3. 配置应用设置 Configure app settings if needed"
Write-Host ""

# 询问是否立即测试
$response = Read-Host "是否现在测试应用程序？Test the application now? (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Start-Process -FilePath "$OutputPath\启动应用_StartApp.bat"
}
