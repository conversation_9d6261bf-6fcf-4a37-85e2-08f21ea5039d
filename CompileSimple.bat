@echo off
echo Compiling Simple Patriotic Presentation App...

set CSC="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
set OUTPUT_DIR=SimpleApp

echo Checking compiler...
if not exist %CSC% (
    echo Error: C# compiler not found
    pause
    exit /b 1
)

echo Creating output directory...
if exist %OUTPUT_DIR% rmdir /s /q %OUTPUT_DIR%
mkdir %OUTPUT_DIR%

echo Compiling application...
%CSC% /target:winexe /out:%OUTPUT_DIR%\PatrioticPresentationApp.exe /reference:"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\WPF\PresentationCore.dll" /reference:"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\WPF\PresentationFramework.dll" /reference:"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\WPF\WindowsBase.dll" /reference:"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Xaml.dll" SimpleApp.cs

if %ERRORLEVEL% neq 0 (
    echo Compilation failed
    pause
    exit /b 1
)

echo Creating startup script...
echo @echo off > "%OUTPUT_DIR%\StartApp.bat"
echo echo Starting Patriotic Presentation App... >> "%OUTPUT_DIR%\StartApp.bat"
echo start PatrioticPresentationApp.exe >> "%OUTPUT_DIR%\StartApp.bat"

echo Creating user guide...
echo # Simple Patriotic Presentation App > "%OUTPUT_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\README.txt"
echo This is a simplified version of the Patriotic Presentation App. >> "%OUTPUT_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\README.txt"
echo ## How to use: >> "%OUTPUT_DIR%\README.txt"
echo 1. Double-click StartApp.bat to run the program >> "%OUTPUT_DIR%\README.txt"
echo 2. Click "Load Presentation" button >> "%OUTPUT_DIR%\README.txt"
echo 3. Use navigation buttons or keyboard: >> "%OUTPUT_DIR%\README.txt"
echo    - Space or Right Arrow: Next slide >> "%OUTPUT_DIR%\README.txt"
echo    - Left Arrow: Previous slide >> "%OUTPUT_DIR%\README.txt"
echo    - F11: Fullscreen mode >> "%OUTPUT_DIR%\README.txt"
echo    - Esc: Exit fullscreen >> "%OUTPUT_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\README.txt"
echo ## Features: >> "%OUTPUT_DIR%\README.txt"
echo - 5 sample slides about Chinese patriotic sentiment >> "%OUTPUT_DIR%\README.txt"
echo - Keyboard navigation >> "%OUTPUT_DIR%\README.txt"
echo - Progress tracking >> "%OUTPUT_DIR%\README.txt"
echo - Fullscreen presentation mode >> "%OUTPUT_DIR%\README.txt"

echo.
echo ===================================
echo Compilation completed!
echo ===================================
echo Output directory: %OUTPUT_DIR%
echo.
echo Next steps:
echo 1. Go to %OUTPUT_DIR% directory
echo 2. Run StartApp.bat
echo 3. Check README.txt for usage instructions
echo.

set /p answer=Test the application now? (y/n): 
if /i "%answer%"=="y" (
    cd %OUTPUT_DIR%
    start StartApp.bat
)

echo.
echo Compilation script completed
pause
