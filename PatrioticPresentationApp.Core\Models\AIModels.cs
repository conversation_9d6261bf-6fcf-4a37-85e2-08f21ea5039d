using System;
using System.Collections.Generic;

namespace PatrioticPresentationApp.Core.Models
{
    /// <summary>
    /// AI service capabilities
    /// </summary>
    public class AICapabilities
    {
        public bool SupportsContentSuggestions { get; set; }
        public bool SupportsQAAssistance { get; set; }
        public bool SupportsAdaptiveLearning { get; set; }
        public bool SupportsSpeechToText { get; set; }
        public bool SupportsTextToSpeech { get; set; }
        public bool SupportsOfflineMode { get; set; }
        public List<string> SupportedLanguages { get; set; } = new List<string> { "zh-CN", "en-US" };
    }

    /// <summary>
    /// Content suggestion from AI
    /// </summary>
    public class ContentSuggestion
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public SuggestionType Type { get; set; }
        public string Content { get; set; }
        public double Confidence { get; set; }
        public string Source { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// AI response to a question
    /// </summary>
    public class AIResponse
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Question { get; set; }
        public string Answer { get; set; }
        public double Confidence { get; set; }
        public List<string> Sources { get; set; } = new List<string>();
        public List<string> RelatedTopics { get; set; } = new List<string>();
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public TimeSpan ResponseTime { get; set; }
    }

    /// <summary>
    /// Presentation context for AI analysis
    /// </summary>
    public class PresentationContext
    {
        public string PresentationId { get; set; }
        public int CurrentSlideIndex { get; set; }
        public List<string> PreviousTopics { get; set; } = new List<string>();
        public AudienceProfile AudienceProfile { get; set; }
        public Dictionary<string, object> ContextData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Audience profile for personalization
    /// </summary>
    public class AudienceProfile
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; }
        public AudienceType Type { get; set; }
        public DifficultyLevel PreferredDifficulty { get; set; }
        public List<string> Interests { get; set; } = new List<string>();
        public List<string> PriorKnowledge { get; set; } = new List<string>();
        public LearningStyle PreferredLearningStyle { get; set; }
        public string PreferredLanguage { get; set; } = "zh-CN";
        public AccessibilityNeeds AccessibilityNeeds { get; set; } = new AccessibilityNeeds();
    }

    /// <summary>
    /// Adaptive learning path
    /// </summary>
    public class AdaptiveLearningPath
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ProfileId { get; set; }
        public string PresentationId { get; set; }
        public List<int> RecommendedSlides { get; set; } = new List<int>();
        public TimeSpan EstimatedDuration { get; set; }
        public DifficultyLevel DifficultyLevel { get; set; }
        public List<string> LearningObjectives { get; set; } = new List<string>();
        public List<AssessmentPoint> AssessmentPoints { get; set; } = new List<AssessmentPoint>();
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Learning progress tracking
    /// </summary>
    public class LearningProgress
    {
        public string PathId { get; set; }
        public string UserId { get; set; }
        public int CompletedSlides { get; set; }
        public int TotalSlides { get; set; }
        public double CompletionPercentage => TotalSlides > 0 ? (double)CompletedSlides / TotalSlides * 100 : 0;
        public List<AssessmentResult> AssessmentResults { get; set; } = new List<AssessmentResult>();
        public TimeSpan TimeSpent { get; set; }
        public DateTime LastActivity { get; set; } = DateTime.Now;
        public LearningStatus Status { get; set; }
    }

    /// <summary>
    /// Assessment point in learning path
    /// </summary>
    public class AssessmentPoint
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public int SlideIndex { get; set; }
        public string Question { get; set; }
        public List<string> Options { get; set; } = new List<string>();
        public string CorrectAnswer { get; set; }
        public string Explanation { get; set; }
        public AssessmentType Type { get; set; }
    }

    /// <summary>
    /// Assessment result
    /// </summary>
    public class AssessmentResult
    {
        public string AssessmentId { get; set; }
        public string UserAnswer { get; set; }
        public bool IsCorrect { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public TimeSpan TimeToAnswer { get; set; }
    }

    /// <summary>
    /// Audience engagement analysis
    /// </summary>
    public class AudienceEngagementAnalysis
    {
        public double OverallEngagementScore { get; set; }
        public Dictionary<int, double> SlideEngagementScores { get; set; } = new Dictionary<int, double>();
        public List<EngagementInsight> Insights { get; set; } = new List<EngagementInsight>();
        public List<string> Recommendations { get; set; } = new List<string>();
        public DateTime AnalysisDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Content effectiveness report
    /// </summary>
    public class ContentEffectivenessReport
    {
        public string SlideId { get; set; }
        public double EffectivenessScore { get; set; }
        public List<string> StrengthAreas { get; set; } = new List<string>();
        public List<string> ImprovementAreas { get; set; } = new List<string>();
        public List<ContentRecommendation> Recommendations { get; set; } = new List<ContentRecommendation>();
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Presentation insights from AI analysis
    /// </summary>
    public class PresentationInsights
    {
        public string PresentationId { get; set; }
        public double OverallScore { get; set; }
        public List<InsightCategory> Categories { get; set; } = new List<InsightCategory>();
        public List<string> KeyFindings { get; set; } = new List<string>();
        public List<string> ActionableRecommendations { get; set; } = new List<string>();
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Voice settings for text-to-speech
    /// </summary>
    public class VoiceSettings
    {
        public string VoiceId { get; set; } = "zh-CN-XiaoxiaoNeural";
        public double Speed { get; set; } = 1.0;
        public double Pitch { get; set; } = 1.0;
        public double Volume { get; set; } = 1.0;
        public string Language { get; set; } = "zh-CN";
        public VoiceGender Gender { get; set; } = VoiceGender.Female;
    }

    /// <summary>
    /// Speech analysis results
    /// </summary>
    public class SpeechAnalysis
    {
        public double Clarity { get; set; }
        public double Pace { get; set; }
        public double Volume { get; set; }
        public List<string> DetectedEmotions { get; set; } = new List<string>();
        public List<PauseAnalysis> Pauses { get; set; } = new List<PauseAnalysis>();
        public List<string> Recommendations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Feedback data from audience
    /// </summary>
    public class FeedbackData
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string UserId { get; set; }
        public int SlideIndex { get; set; }
        public FeedbackType Type { get; set; }
        public string Content { get; set; }
        public int Rating { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    // Supporting classes
    public class EngagementInsight
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public double Impact { get; set; }
    }

    public class ContentRecommendation
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public int Priority { get; set; }
    }

    public class InsightCategory
    {
        public string Name { get; set; }
        public double Score { get; set; }
        public List<string> Details { get; set; } = new List<string>();
    }

    public class PauseAnalysis
    {
        public TimeSpan StartTime { get; set; }
        public TimeSpan Duration { get; set; }
        public PauseType Type { get; set; }
    }

    public class AccessibilityNeeds
    {
        public bool RequiresHighContrast { get; set; }
        public bool RequiresLargeText { get; set; }
        public bool RequiresScreenReader { get; set; }
        public bool RequiresCaptions { get; set; }
        public bool RequiresSlowTransitions { get; set; }
    }

    // Enumerations
    public enum SuggestionType
    {
        AdditionalContent,
        InteractiveElement,
        Transition,
        Timing,
        Engagement,
        Accessibility
    }

    public enum AudienceType
    {
        Students,
        Professionals,
        GeneralPublic,
        Educators,
        Researchers
    }

    public enum DifficultyLevel
    {
        Beginner,
        Intermediate,
        Advanced,
        Expert
    }

    public enum LearningStyle
    {
        Visual,
        Auditory,
        Kinesthetic,
        ReadingWriting,
        Mixed
    }

    public enum LearningStatus
    {
        NotStarted,
        InProgress,
        Completed,
        Paused,
        Failed
    }

    public enum AssessmentType
    {
        MultipleChoice,
        TrueFalse,
        ShortAnswer,
        Essay,
        Interactive
    }

    public enum VoiceGender
    {
        Male,
        Female,
        Neutral
    }

    public enum FeedbackType
    {
        Rating,
        Comment,
        Question,
        Suggestion,
        Complaint
    }

    public enum PauseType
    {
        Natural,
        Hesitation,
        Emphasis,
        Breath
    }

    // Event Arguments for AI Services
    public class AIResponseEventArgs : EventArgs
    {
        public AIResponse Response { get; }

        public AIResponseEventArgs(AIResponse response)
        {
            Response = response;
        }
    }

    public class ContentSuggestionEventArgs : EventArgs
    {
        public List<ContentSuggestion> Suggestions { get; }

        public ContentSuggestionEventArgs(List<ContentSuggestion> suggestions)
        {
            Suggestions = suggestions;
        }
    }

    public class LearningPathEventArgs : EventArgs
    {
        public AdaptiveLearningPath LearningPath { get; }

        public LearningPathEventArgs(AdaptiveLearningPath learningPath)
        {
            LearningPath = learningPath;
        }
    }
}
